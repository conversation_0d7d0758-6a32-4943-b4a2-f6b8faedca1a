'use server'

import { db, openai, schema } from '@packages/shared'
import { and, cosineDistance, desc, eq, getTableColumns, gt, sql } from 'drizzle-orm'
import { verifyAuthToken } from '@/utils/firebase-admin'
import { mq } from '@packages/shared'
import { Queue } from 'bullmq'
import { redditApiRequest } from '@packages/shared'
import axios from 'axios'
import { hasActiveSubscription } from './subscription'
import { generate } from "random-words"

const queue = new Queue('parse-posts', mq.options)
interface RedditResponse {
	data: {
		children: Array<{
			kind: string
			data: any
		}>
		after: string | null
	}
}
interface CreateProjectParams {
	userId: string
	name: string
	url: string
	description: string
	painPoint: string
	businessType: string
	averageSaleValue: number
	keywords: string
	aiEnabled: boolean
	primaryKeyword: string
}

export async function createProject(params: CreateProjectParams) {
	try {
		// Create new project
		const result = await db
			.insert(schema.project)
			.values({
				userId: params.userId,
				name: params.name,
				url: params.url,
				description: params.description,
				painPoint: params.painPoint,
				businessType: params.businessType,
				averageSaleValue: params.averageSaleValue,
				keywords: params.keywords,
				aiEnabled: params.aiEnabled,
				primaryKeyword: params.primaryKeyword,
			})
			.returning({ id: schema.project.id })

		return { success: true, id: result[0].id }
	} catch (error) {
		console.error('Error creating project:', error)
		return { success: false, error }
	}
}

const getProjectSubreddits = async (projectId: string) => {
	const { embedding, description, descriptionHtml, publicDescriptionHtml, ...allColumns } = getTableColumns(
		schema.subreddit,
	)
	return await db
		.select({
			...allColumns,
			selected: schema.projectSubreddit.selected,
			aiSuggested: schema.projectSubreddit.aiSuggested,
		})
		.from(schema.projectSubreddit)
		.where(eq(schema.projectSubreddit.projectId, projectId))
		.leftJoin(schema.subreddit, eq(schema.projectSubreddit.subredditId, schema.subreddit.id))
		.orderBy(desc(schema.projectSubreddit.selected))
}

const handleKeyword = async (keyword: string, { description, painPoint, projectId }: { description: string, painPoint: string, projectId: string }) => {
	let responseData: RedditResponse
	const hasRedditCredentials = process.env.REDDIT_CLIENT_ID && process.env.REDDIT_CLIENT_SECRET
	if (hasRedditCredentials) {
		// Use authenticated Reddit API
		responseData = await redditApiRequest(
			`/subreddits/search.json?q=${encodeURI(keyword || '')}`,
			process.env.REDDIT_CLIENT_ID || '',
			process.env.REDDIT_CLIENT_SECRET || '',
			{ method: 'GET' },
		)
	} else {
		// Fallback to public API
		const { data } = await axios.get(`https://www.reddit.com/subreddits/search.json?q=${encodeURI(keyword || '')}`, {
			headers: { 'User-Agent': 'leadsautomatic/1.0 (by /u/f4yis)' },
		})
		responseData = data
	}
	const data = responseData.data.children.slice(0, 5)
	for (let i = 0; i < data.length; i++) {
		const subredditData = data[i].data
		const promot = `You are a helpful assistant. Determine whether the given subreddit is a suitable place to post a specific project based on the subreddit's description, the project's description.

			Inputs:

			Subreddit Description: ${subredditData.public_description}

			Project Description: ${description}
			Project painpoint: ${painPoint}

			Task:
			Evaluate whether the project's content is appropriate for the subreddit based on relevance to the subreddit's purpose, topic alignment.

			Output Format:
			Return a JSON response in the following format:
			{
			"match": true | false
			}
			Only return true if there is a clear alignment in terms of subject matter and posting guidelines. Otherwise, return false.
			`
		if(typeof openai === 'undefined' || !openai?.ai) {
			return { success: false, error: 'OpenAI is not set' }
		}
		const result = await openai?.ai?.chat?.completions?.create({
			model: 'gpt-4o-mini',
			messages: [{ role: 'user', content: promot }],
			response_format: { type: 'json_object' },
		})
		
		const match = result?.choices[0].message.content?.includes('true')
		let subredditId = ''
		const subreddit = await db.query.subreddit.findFirst({
			where: eq(schema.subreddit.redditId, subredditData.id),
			columns: {
				id: true,
			}
		})
		if (!subreddit) {
			try {
				const result = await db.insert(schema.subreddit).values({
					redditId: subredditData.id,
					name: subredditData.name,
					displayName: subredditData.display_name,
					prefixedName: subredditData.display_name_prefixed,
					url: subredditData.url,

					// Content
					title: subredditData.title,
					description: subredditData.description,
					descriptionHtml: subredditData.description_html,
					publicDescription: subredditData.public_description,
					publicDescriptionHtml: subredditData.public_description_html,

					// Stats
					subscribers: subredditData.subscribers,
					activeUserCount: subredditData.active_user_count,

					// Type information
					kind: data[i].kind,
					subredditType: subredditData.subreddit_type,
					over18: subredditData.over18,

					// Features
					allowImages: subredditData.allow_images,
					allowVideos: subredditData.allow_videos,
					allowGalleries: subredditData.allow_galleries,
					restrictPosting: subredditData.restrict_posting,

					// Icons and images
					iconImg: subredditData.icon_img,
					communityIcon: subredditData.community_icon,
					bannerImg: subredditData.banner_img,

					redditCreatedUtc: subredditData.reddit_created_utc,
				}).returning({ id: schema.subreddit.id })
				subredditId = result[0].id
			} catch (error) {
				console.error('Error creating subreddit:', error)
			}

		} else {
			subredditId = subreddit.id
		}
		if (subredditId) {
			await db.insert(schema.projectSubreddit).values({
				projectId,
				subredditId,
				aiSuggested: match,
				selected: match,
			})
		}
	}
}

export async function aiSuggestSubreddits(projectId: string) {
	const [project, subreddits] = await Promise.all([
		db.query.project.findFirst({
			where: eq(schema.project.id, projectId),
		}),
		getProjectSubreddits(projectId),
	])
	if (subreddits.length > 0) {
		return { success: true, data: subreddits }
	}
	if (!project) {
		return { success: false, error: 'Project not found' }
	}
	const split = project.keywords.split(',')
	for (let i = 0; i < split.length; i++) {
		await handleKeyword(split[i], { description: project.description, painPoint: project.painPoint, projectId })
	}
	return { success: true, data: await getProjectSubreddits(projectId) }
// 	const embedding = await openai.generateEmbedding(project.keywords)
// 	const similarity = sql<number>`1 - (${cosineDistance(schema.subreddit.embedding, embedding)})`
// 	const similarSubreddits = await db
// 		.select({ id: schema.subreddit.id, similarity, displayName: schema.subreddit.displayName, publicDescription: schema.subreddit.publicDescription })
// 		.from(schema.subreddit)
// 		.where(gt(similarity, 0.5))
// 		.orderBy((t: any) => desc(t.similarity))
// 		.limit(40)
// 	if (!similarSubreddits.length) {
// 		return { success: true, data: [] }
// 	}
// 	const subredditsToInsert = similarSubreddits.map((subreddit: any) => openai?.ai?.chat?.completions?.create({
// 		model: 'gpt-4o-mini',
// 		messages: [{
// 			role: 'user', content: `
// 			You are a helpful assistant. Determine whether the given subreddit is a suitable place to post a specific project based on the subreddit's description, the project's description, and the relevant keywords.

// Inputs:

// Subreddit Description: ${subreddit.publicDescription}

// Project Description: ${project.description}

// Project Keywords: ${project.keywords}

// Task:
// Evaluate whether the project's content is appropriate for the subreddit based on relevance to the subreddit's purpose, topic alignment, and use of keywords.
// Output Format:
// Return a JSON response in the following format:
// {
//   "match": true | false
// }
// Only return true if there is a clear alignment in terms of subject matter and posting guidelines. Otherwise, return false.
// 		` }],
// 		response_format: { type: 'json_object' },
// 	}))
// 	const results = await Promise.all(subredditsToInsert)
// 	const matches: any = []
// 	for (let i = 0; i < results.length; i++) {
// 		const match = results[i].choices[0].message.content.includes('true')
// 		if (match) {
// 			matches.push(similarSubreddits[i].id)
// 		}
// 	}
// 	console.log(matches)
// 	const final = similarSubreddits.map((subreddit: any, index: number) => ({
// 		projectId,
// 		subredditId: subreddit.id,
// 		aiSuggested: true,
// 		selected: index < 10,
// 	})).filter((subreddit: any) => matches.includes(subreddit.subredditId))
// 	await db.insert(schema.projectSubreddit).values(final)
// 	const result = await getProjectSubreddits(projectId)
// 	return { success: true, data: result }
}

export async function getProjectById(id: string) {
	console.log("fetching project by id", id)
	const user = await verifyAuthToken()
	if (!user) {
		return { success: false, error: 'Unauthorized' }
	}
	try {
		const project = await db.query.project.findFirst({
			where: and(eq(schema.project.id, id), eq(schema.project.userId, user.uid)),
		})

		return { success: true, project }
	} catch (error) {
		console.error('Error fetching project:', error)
		return { success: false, error }
	}
}

/**
 * Get all projects for a user
 * @returns List of projects or error
 */
export async function getUserProjects() {
	try {
		// Verify the auth token
		const user = await verifyAuthToken()
		if (!user) {
			return { success: false, error: 'Unauthorized' }
		}
		// Use the prepared query approach which should be more compatible
		const projects = await db.query.project.findMany({
			where: (fields: any, { eq }: any) => eq(fields.userId, user.uid),
		})

		return { success: true, projects }
	} catch (error) {
		console.error('Error fetching user projects:', error)
		return { success: false, error }
	}
}

const cache = new Map<string, { timestamp: number }>()

export const checkPostStatus = async (projectId: string) => {
	console.log("checking post status", projectId)
	const user = await verifyAuthToken()
	if (!user) {
		return { success: false, error: 'Unauthorized' }
	}
	const { hasSubscription } = await hasActiveSubscription(user.uid)
	if (!hasSubscription) {
		return { success: true, data: { status: 'completed' } }
	}
	const ownedProject = await db.query.project.findFirst({
		where: and(eq(schema.project.id, projectId), eq(schema.project.userId, user.uid)),
	})
	if (!ownedProject) {
		return { success: false, error: 'Project not found or unauthorized' }
	}
	
	if (cache.has(projectId)) {
		const data = cache.get(projectId)
		if (data?.timestamp && Date.now() - data?.timestamp < 1500) {
			return { success: true, data: { status: 'pending' } }
		}
	}
	cache.set(projectId, { timestamp: Date.now() })
	const [job, project] = await Promise.all([
		queue.getJob(projectId),
		db.query.project.findFirst({
			where: eq(schema.project.id, projectId),
			columns: {
				lastScanCompleted: true,
			},
		}),
	])
	console.log({ project })
	// 24 hours
	if (project?.lastScanCompleted && project.lastScanCompleted > new Date(Date.now() - 1000 * 60 * 60 * 24)) {
		return { success: true, data: { status: 'completed' } }
	}
	console.log({ job })
	if (!job || job.progress === 'completed') {
		console.log('Adding job to queue')
		await queue.remove(projectId)
		await queue.add(projectId, { id: projectId }, { jobId: projectId })
		return { success: true, data: { status: 'pending' } }
	}
	console.log({ job })

	return { success: true, data: { status: job.progress === 'completed' ? 'completed' : 'pending' } }
}

const demoPosts = async (projectId: string) => {
	const subreddits = await db.select({
		subredditId: schema.projectSubreddit.subredditId,
		subredditName: schema.subreddit.displayName,
	}).from(schema.projectSubreddit).where(and(eq(schema.projectSubreddit.projectId, projectId), eq(schema.projectSubreddit.selected, true))).leftJoin(schema.subreddit, eq(schema.projectSubreddit.subredditId, schema.subreddit.id))
	const posts = subreddits.map((subreddit) => {
		return [{
			title: (generate({min: 3, max: Math.floor(Math.random() * 10) + 4}) as string[])?.join(' '),
			id: Math.random().toString(36).substring(2, 15),
			subreddit: subreddit.subredditName,
			selftext: (generate({min: 8, max: Math.floor(Math.random() * 10) + 10}) as string[])?.join(' '),
			chosenReason: 'Hidden reason, subscribe to see',
			redditId: '123',
			author: 'Hidden author, subscribe to see',
			permalink: '#',
			url: '#',
			created: new Date(),
			score: 100,
			numComments: 100,
			thumbnail: 'https://via.placeholder.com/150',
			isOriginalContent: true,
			isVideo: true,
			upvoteRatio: 0.5,
			sendReplies: true,
			status: 'pending',
			chosen: true,
			chosenAt: new Date(),
			relevanceScore: 0.5,
			subscribers: 10000,
			iconImg: 'https://via.placeholder.com/150',
		}, {
			title: (generate({min: 3, max: Math.floor(Math.random() * 10) + 4}) as string[])?.join(' '),
			id: Math.random().toString(36).substring(2, 15),
			subreddit: subreddit.subredditName,
			selftext: (generate({min: 8, max: Math.floor(Math.random() * 10) + 10}) as string[])?.join(' '),
			chosenReason: 'Hidden reason, subscribe to see',
			redditId: '123',
			author: 'Hidden author, subscribe to see',
			permalink: '#',
			url: '#',
			created: new Date(),
			score: 100,
			numComments: 100,
			thumbnail: 'https://via.placeholder.com/150',
			isOriginalContent: true,
			isVideo: true,
			upvoteRatio: 0.5,
			sendReplies: true,
			status: 'pending',
			chosen: true,
			chosenAt: new Date(),
			relevanceScore: 0.5,
			subscribers: 10000,
			iconImg: 'https://via.placeholder.com/150',
		}]
	})
	console.log({ posts })
	return posts.flat()
}

export const getPosts = async (projectId: string) => {
	console.log("getting posts", projectId)
	const user = await verifyAuthToken()
	if (!user) {
		return { success: false, error: 'Unauthorized' }
	}
	const projectRecord = await db.query.project.findFirst({
		where: and(eq(schema.project.id, projectId), eq(schema.project.userId, user.uid)),
	})
	if (!projectRecord) {
		return { success: false, error: 'Project not found or unauthorized' }
	}
	const { hasSubscription } = await hasActiveSubscription(user.uid)
	if (!hasSubscription) {
		return { success: true, data: await demoPosts(projectId) }
	}

	const posts = await db
		.select({
			
			title: hasSubscription ? schema.post.title : sql`'Hidden title, subscribe to see'`,
			id: schema.post.id,
			subreddit: schema.post.subreddit,
			selftext: hasSubscription ? schema.post.selftext : sql`'Hidden long text the reddit post contains. goto /plans to see pricing and subscribe for 29$, subscribe to see'`,
			chosenReason: hasSubscription ? schema.post.chosenReason : sql`'Hidden reason, subscribe to see'`,
			redditId: hasSubscription ? schema.post.redditId : sql`'Hidden reddit id, subscribe to see'`,
			author: hasSubscription ? schema.post.author : sql`'Hidden author, subscribe to see'`,
			permalink: hasSubscription ? schema.post.permalink : sql`'#'`,
			url: hasSubscription ? schema.post.url : sql`'#'`,
			created: schema.post.created,
			score: schema.post.score,
			numComments: schema.post.numComments,
			thumbnail: schema.post.thumbnail,
			isOriginalContent: schema.post.isOriginalContent,
			isVideo: schema.post.isVideo,
			upvoteRatio: schema.post.upvoteRatio,
			sendReplies: schema.post.sendReplies,
			status: schema.post.status,
			chosen: schema.post.chosen,
			chosenAt: schema.post.chosenAt,
			relevanceScore: schema.post.relevanceScore,
			subscribers: schema.subreddit.subscribers,
			iconImg: schema.subreddit.iconImg,
		})
		.from(schema.post)
		.where(and(eq(schema.post.projectId, projectId), eq(schema.post.chosen, true)))
		.orderBy(desc(schema.post.score))
		.leftJoin(schema.subreddit, eq(schema.post.subreddit, schema.subreddit.displayName))
	// .groupBy(schema.post.id)

	return { success: true, data: posts }
}
