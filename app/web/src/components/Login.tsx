'use client'

import Link from 'next/link'
import { useState } from 'react'
import { signInWithEmailAndPassword, GoogleAuthProvider, signInWithPopup } from 'firebase/auth'
import { auth } from '@/utils/firebase'
import { useRouter } from 'next/navigation'
import { env } from 'next-runtime-env'
import Image from 'next/image'
export default function Login() {
	const [email, setEmail] = useState('')
	const [password, setPassword] = useState('')
	const [error, setError] = useState('')
	const [loading, setLoading] = useState(false)
	const router = useRouter()

	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault()
		// return
		setError('')
		setLoading(true)

		// Basic validation
		if (!email || !password) {
			setError('Please fill in all fields')
			setLoading(false)
			return
		}

		try {
			// Sign in with email and password
			await signInWithEmailAndPassword(auth, email, password)
			// Redirect to start project page
			router.push('/project')
		} catch (error: any) {
			console.error('Login error:', error)
			setError(error.message || 'Failed to login')
		} finally {
			setLoading(false)
		}
	}

	const handleGoogleLogin = async () => {
		setError('')
		setLoading(true)

		try {
			const provider = new GoogleAuthProvider()
			await signInWithPopup(auth, provider)
			// Redirect to start project page
			router.push('/project')
		} catch (error: any) {
			console.error('Google login error:', error)
			setError(error.message || 'Failed to login with Google')
		} finally {
			setLoading(false)
		}
	}

	return (
		<div className="min-h-[calc(100vh-79px)] flex flex-col md:flex-row bg-[#fffaf7]">
			{/* Left: Login Form */}
			<div className="flex-3 flex flex-col justify-center items-center p-8">
				<div className="w-full max-w-md bg-white rounded-3xl shadow-lg p-8">
					{/* Logo and Welcome */}
					<div className="mb-8 text-left">
						<div className="text-gray-500 text-sm mb-1">Welcome back</div>
						<h2 className="text-3xl font-bold mb-2">Log In</h2>
					</div>

					{error && (
						<div className="alert alert-error mb-4">
							<span>{error}</span>
						</div>
					)}

					<form className="space-y-4" onSubmit={handleLogin}>
						<div>
							<label className="block text-sm font-medium mb-1" htmlFor="email">Email</label>
							<input
								type="email"
								id="email"
								className="input w-full bg-[#eaf0fa] border-none rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
								placeholder="<EMAIL>"
								value={email}
								onChange={(e) => setEmail(e.target.value)}
								disabled={loading}
							/>
						</div>
						<div>
							<label className="block text-sm font-medium mb-1" htmlFor="password">Password</label>
							<input
								type="password"
								id="password"
								className="input w-full bg-[#eaf0fa] border-none rounded-lg focus:ring-2 focus:ring-primary focus:outline-none"
								placeholder="************"
								value={password}
								onChange={(e) => setPassword(e.target.value)}
								disabled={loading}
							/>
							<div className="text-right mt-1">
								<Link href="#" className="text-xs text-primary hover:underline">Forgot Password ?</Link>
							</div>
						</div>
						<button type="submit" className="btn w-full bg-[#FF4500] hover:bg-[#e03d00] text-white rounded-full py-2 mt-2 transition-colors" disabled={loading}>
							{loading ? 'Logging in...' : 'LOGIN'}
						</button>
						<div className="flex items-center my-4">
							<div className="flex-grow h-px bg-gray-200" />
							<span className="mx-2 text-xs text-gray-400">or continue with</span>
							<div className="flex-grow h-px bg-gray-200" />
						</div>
						<div className="flex gap-3 justify-center">
							<button
								type="button"
								className="border cursor-pointer border-gray-200 rounded-full p-2 hover:bg-gray-100 transition-colors"
								onClick={handleGoogleLogin}
								disabled={loading}
								aria-label="Login with Google"
							>
								<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20">
									<title>Google</title>
									<path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
									<path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
									<path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
									<path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
								</svg>
							</button>
							{/* Add more social buttons here if needed */}
						</div>
					</form>
					<div className="text-center mt-6">
						<span className="text-gray-500 text-sm">Don't have an account yet? </span>
						<Link href="/signup" className="text-primary font-medium hover:underline ml-1">Sign up for free</Link>
					</div>
				</div>
			</div>
			{/* Right: Illustration */}
			<div className="hidden md:flex flex-2 items-center justify-center bg-[#FF4500]">
			</div>
		</div>
	)
}
