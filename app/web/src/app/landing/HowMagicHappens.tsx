import { Button } from '@/components/ui/button'
import { ArrowRight } from 'lucide-react'
import Link from 'next/link'
const FeatureCard = ({ title, description }: { title: string; description: string }) => {
	return (
		<div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6">
			<h3 className="font-bold text-lg mb-3">{title}</h3>
			<p className="text-gray-600 text-sm">{description}</p>
		</div>
	)
}

const HowMagicHappens = () => {
	return (
		<section className="py-20">
			<div className="max-w-6xl mx-auto px-4">
				<div className="mb-16">
					<div className="flex flex-col md:flex-row items-center justify-between gap-8">
						<div>
							<p className="text-gray-500 uppercase tracking-wider text-sm mb-2">HOW IT WORKS</p>
							<h2 className="text-4xl md:text-5xl font-bold mb-4">
								Now we <span className="text-[#FF4500]">make magic</span> happen
							</h2>
						</div>

						<div className="max-w-md">
							<p className="text-gray-600">
								Let Leads Automatic scrapes Reddit for real buyers and response suggestions while you sleep.
							</p>
							<Link href="/signup">
								<Button className="mt-4 bg-[#FF4500] hover:bg-[#e03d00] text-white">
									Find leads <ArrowRight className="ml-2 h-4 w-4" />
								</Button>
							</Link>
						</div>
					</div>
				</div>

				<div className="grid md:grid-cols-3 gap-8">
					<div className="flex flex-col space-y-4">
						<div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6 relative overflow-hidden">
							<div className="relative z-10">
								<h3 className="font-bold text-lg mb-3">Deep analysis of your business</h3>
								<p className="text-gray-600 text-sm mb-6">
									We analyze your product, target audience, and Reddit community preferences. Discover
									high-impact subreddits with relevant conversations and low competition. Then we
									start monitoring and crawling those for relevant keywords.
								</p>
								<Link href="/signup">
									<div className="bg-[#FF4500] text-white py-2 px-4 rounded-lg cursor-pointer hover:bg-[#e03d00] transition-colors">
										Analyze your product
									</div>
								</Link>
							</div>
						</div>
					</div>

					<div className="flex flex-col space-y-4">
						<div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6 relative overflow-hidden">
							<div className="relative z-10">
								<h3 className="font-bold text-lg mb-3">Get a powerful 30-day plan</h3>
								<p className="text-gray-600 text-sm mb-6">
									Position your account as a thought leader in your niche and makes sure you don't get banned.
								</p>

								<div className="relative">
									<div className="grid grid-cols-2 gap-2">
										<div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
											<div className="text-xs font-medium text-gray-700 mb-1">Apr 20</div>
											<div className="text-xs text-gray-500">
												Find posts about product discovery
											</div>
										</div>
										<div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
											<div className="text-xs font-medium text-gray-700 mb-1">Apr 21</div>
											<div className="text-xs text-gray-500">Engage with r/SaaS community</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div className="flex flex-col space-y-4">
						<div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6 relative overflow-hidden">
							<div className="relative z-10">
								<h3 className="font-bold text-lg mb-3">Generate replies on autopilot</h3>
								<p className="text-gray-600 text-sm mb-6">
									We create authentic-sounding, personalized reply suggestions based on ongoing Reddit
									conversations. In your Dashboard, you'll see up to 100+ opportunities daily for maximum growth.
								</p>

								<div className="flex items-center justify-between">
									<div className="flex-1 bg-[#fff1eb] rounded-full h-2.5">
										<div className="bg-[#FF4500] h-2.5 rounded-full" style={{ width: '97%' }}></div>
									</div>
									<div className="ml-4 text-2xl font-bold text-gray-900">97%</div>
								</div>
								<div className="mt-2 flex justify-between text-xs text-gray-500">
									<div>Reddit Quality Score</div>
									<div>Words: 175, Tone: Helpful</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	)
}

export default HowMagicHappens
