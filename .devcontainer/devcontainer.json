{"image": "mcr.microsoft.com/devcontainers/javascript-node:20", "features": {"ghcr.io/devcontainers/features/sshd:1": {"version": "latest"}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"version": "latest"}}, "mounts": ["source=/var/run/docker-host.sock,target=/var/run/docker.sock,type=bind"], "postCreateCommand": "npm install -g @withgraphite/graphite-cli@stable && curl -fsSL https://bun.sh/install | bash"}