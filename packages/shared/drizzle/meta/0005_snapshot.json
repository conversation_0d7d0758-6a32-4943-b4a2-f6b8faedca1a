{"id": "2b78242f-8c6b-4ace-aa5c-88ec7674e5b3", "prevId": "843ed684-1726-455a-bac9-001a8b5ac53b", "version": "7", "dialect": "postgresql", "tables": {"public.subreddit": {"name": "subreddit", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "reddit_id": {"name": "reddit_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "prefixed_name": {"name": "prefixed_name", "type": "<PERSON><PERSON><PERSON>(102)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "description_html": {"name": "description_html", "type": "text", "primaryKey": false, "notNull": false}, "public_description": {"name": "public_description", "type": "text", "primaryKey": false, "notNull": false}, "public_description_html": {"name": "public_description_html", "type": "text", "primaryKey": false, "notNull": false}, "subscribers": {"name": "subscribers", "type": "integer", "primaryKey": false, "notNull": false}, "active_user_count": {"name": "active_user_count", "type": "integer", "primaryKey": false, "notNull": false}, "kind": {"name": "kind", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "subreddit_type": {"name": "subreddit_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "over18": {"name": "over18", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "allow_images": {"name": "allow_images", "type": "boolean", "primaryKey": false, "notNull": false}, "allow_videos": {"name": "allow_videos", "type": "boolean", "primaryKey": false, "notNull": false}, "allow_galleries": {"name": "allow_galleries", "type": "boolean", "primaryKey": false, "notNull": false}, "restrict_posting": {"name": "restrict_posting", "type": "boolean", "primaryKey": false, "notNull": false}, "icon_img": {"name": "icon_img", "type": "text", "primaryKey": false, "notNull": false}, "community_icon": {"name": "community_icon", "type": "text", "primaryKey": false, "notNull": false}, "banner_img": {"name": "banner_img", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reddit_created_utc": {"name": "reddit_created_utc", "type": "timestamp", "primaryKey": false, "notNull": false}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": false}}, "indexes": {"reddit_id_idx": {"name": "reddit_id_idx", "columns": [{"expression": "reddit_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "embeddingIndex": {"name": "embeddingIndex", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subreddit_reddit_id_unique": {"name": "subreddit_reddit_id_unique", "nullsNotDistinct": false, "columns": ["reddit_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"email_idx": {"name": "email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "pain_point": {"name": "pain_point", "type": "text", "primaryKey": false, "notNull": true}, "business_type": {"name": "business_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "average_sale_value": {"name": "average_sale_value", "type": "integer", "primaryKey": false, "notNull": true}, "ai_enabled": {"name": "ai_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_user_id_user_id_fk": {"name": "project_user_id_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription": {"name": "subscription", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'trial'"}, "is_trial_active": {"name": "is_trial_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "trial_start_date": {"name": "trial_start_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "trial_end_date": {"name": "trial_end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscription_user_id_user_id_fk": {"name": "subscription_user_id_user_id_fk", "tableFrom": "subscription", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.plan": {"name": "plan", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "lemonsqueezy_variant_id": {"name": "lemonsqueezy_variant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "lemonsqueezy_product_id": {"name": "lemonsqueezy_product_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "interval": {"name": "interval", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "max_projects": {"name": "max_projects", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"lemonsqueezy_variant_id_idx": {"name": "lemonsqueezy_variant_id_idx", "columns": [{"expression": "lemonsqueezy_variant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"plan_lemonsqueezy_variant_id_unique": {"name": "plan_lemonsqueezy_variant_id_unique", "nullsNotDistinct": false, "columns": ["lemonsqueezy_variant_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}