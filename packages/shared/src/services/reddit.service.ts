/**
 * Reddit API Service - FOR INTERNAL USE ONLY
 *
 * This module provides utilities for interacting with the Reddit API.
 * It is intended for backend processes only and should not be exposed via external APIs.
 * All communication with Reddit happens directly from the backend processes.
 */

import axios from 'axios'

// Reddit API constants
const REDDIT_TOKEN_URL = 'https://www.reddit.com/api/v1/access_token'
const USER_AGENT = 'leadsautomatic/1.0 (by /u/f4yis)'

// Cache for the access token
let accessTokenCache = {
	token: '',
	expiresAt: 0, // Unix timestamp when the token expires
}

/**
 * Get a Reddit API access token using client credentials flow
 * For internal use only - not exposed via API
 *
 * @param clientId - Reddit API client ID
 * @param clientSecret - Reddit API client secret
 * @returns The access token
 */
export const getRedditAccessToken = async (clientId: string, clientSecret: string): Promise<string> => {
	const now = Math.floor(Date.now() / 1000)

	// Return cached token if it's still valid (with 60-second buffer)
	if (accessTokenCache.token && accessTokenCache.expiresAt > now + 60) {
		return accessTokenCache.token
	}

	try {
		// Create basic auth credentials
		const authString = Buffer.from(`${clientId}:${clientSecret}`).toString('base64')

		// Make request to get access token
		const response = await axios.post(REDDIT_TOKEN_URL, 'grant_type=client_credentials', {
			headers: {
				Authorization: `Basic ${authString}`,
				'Content-Type': 'application/x-www-form-urlencoded',
				'User-Agent': USER_AGENT,
			},
		})

		// Cache the token with expiration time
		const { access_token, expires_in } = response.data
		accessTokenCache = {
			token: access_token,
			expiresAt: now + expires_in,
		}

		return access_token
	} catch (error) {
		console.error('Error obtaining Reddit access token:', error)
		throw new Error('Failed to obtain Reddit access token')
	}
}

/**
 * Make an authenticated request to the Reddit API
 * For internal use only - not exposed via API
 *
 * @param endpoint - API endpoint to call (without the base URL)
 * @param clientId - Reddit API client ID
 * @param clientSecret - Reddit API client secret
 * @param options - Additional axios request options
 * @returns The API response data
 */
export const redditApiRequest = async (
	endpoint: string,
	clientId: string,
	clientSecret: string,
	options: any = {},
): Promise<any> => {
	try {
		const accessToken = await getRedditAccessToken(clientId, clientSecret)

		// Prepare request options with authentication
		const requestOptions = {
			...options,
			headers: {
				...options.headers,
				Authorization: `Bearer ${accessToken}`,
				'User-Agent': USER_AGENT,
			},
		}

		// Make the authenticated request
		const response = await axios({
			url: `https://oauth.reddit.com${endpoint}`,
			...requestOptions,
		})

		return response.data
	} catch (error) {
		console.error(`Error making authenticated request to ${endpoint}:`, error)
		throw error
	}
}
