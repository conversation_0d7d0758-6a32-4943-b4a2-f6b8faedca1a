CREATE TABLE "subreddit" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"reddit_id" varchar(10) NOT NULL,
	"name" varchar(50) NOT NULL,
	"display_name" varchar(100) NOT NULL,
	"prefixed_name" varchar(102) NOT NULL,
	"url" varchar(255) NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text NOT NULL,
	"description_html" text,
	"public_description" text,
	"public_description_html" text,
	"subscribers" integer,
	"active_user_count" integer,
	"kind" varchar(10),
	"subreddit_type" varchar(50),
	"over18" boolean DEFAULT false,
	"allow_images" boolean,
	"allow_videos" boolean,
	"allow_galleries" boolean,
	"restrict_posting" boolean,
	"icon_img" text,
	"community_icon" text,
	"banner_img" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"reddit_created_utc" timestamp,
	"embedding" vector(1536),
	CONSTRAINT "subreddit_reddit_id_unique" UNIQUE("reddit_id")
);
--> statement-breakpoint
CREATE UNIQUE INDEX "reddit_id_idx" ON "subreddit" USING btree ("reddit_id");--> statement-breakpoint
CREATE INDEX "embeddingIndex" ON "subreddit" USING hnsw ("embedding" vector_cosine_ops);