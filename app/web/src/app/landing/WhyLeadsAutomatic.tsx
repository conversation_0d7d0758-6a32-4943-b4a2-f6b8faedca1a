interface BenefitProps {
	emoji: string
	title: string
	description: string
}

const Benefit = ({ emoji, title, description }: BenefitProps) => {
	return (
		<div className="flex">
			<div className="flex-shrink-0 w-12 h-12 bg-[#fff1eb] rounded-full flex items-center justify-center text-[#FF4500] text-2xl mr-4">
				{emoji}
			</div>
			<div>
				<h3 className="text-xl font-bold mb-2">{title}</h3>
				<p className="text-gray-600">{description}</p>
			</div>
		</div>
	)
}

const WhyLeadsAutomatic = () => {
	const benefits = [
		{
			emoji: '🎯',
			title: 'Laser-focused on discovery',
			description:
				'Specifically designed for small SaaS companies and indie developers looking to get noticed on Reddit.',
		},
		{
			emoji: '⏱',
			title: 'Save hours every week',
			description:
				'No more manual scrolling through subreddits. Focus on building your product while we find engagement opportunities.',
		},
		{
			emoji: '💬',
			title: 'Replies that feel human',
			description:
				'Our AI creates responses that sound natural and add genuine value, not robotic promotional content.',
		},
		{
			emoji: '🤝',
			title: 'Data backed trust',
			description:
				'85% of redditors agree people post things that are honest and truthful. (Talk Shoppe / Reddit, Magic of Reddit, US, Nov 2021)',
		},
	]

	return (
		<section id="why-LeadsAutomatic" className="py-16">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-16">
					<h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Why LeadsAutomatic?</h2>
					<p className="text-xl text-gray-600 max-w-2xl mx-auto">
						Purpose-built for indie developers and small SaaS startups
					</p>
				</div>

				<div className="grid md:grid-cols-2 gap-8">
					{benefits.map((benefit, index) => (
						<Benefit
							key={index}
							emoji={benefit.emoji}
							title={benefit.title}
							description={benefit.description}
						/>
					))}
				</div>
			</div>
		</section>
	)
}

export default WhyLeadsAutomatic
