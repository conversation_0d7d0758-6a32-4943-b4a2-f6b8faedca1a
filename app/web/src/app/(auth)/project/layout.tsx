'use client'
import { useUserStore } from '@/store/userStore'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { hasActiveSubscription } from '@/actions/subscription'

export default function ProjectLayout({ children }: { children: React.ReactNode }) {
	const { user, isLoggedIn } = useUserStore() as { user: any; isLoggedIn: boolean }
	const [loading, setLoading] = useState(true)
	const router = useRouter()

	useEffect(() => {
		// Check if user is logged in
		if (!isLoggedIn && !loading) {
			router.push('/login')
			return
		}

		// Check if user has an active subscription
		// const checkSubscription = async () => {
		// 	try {
		// 		if (!user?.uid) {
		// 			setLoading(false)
		// 			return
		// 		}

		// 		const result = await hasActiveSubscription(user.uid)

		// 		if (!result.success) {
		// 			// Handle error
		// 			router.push('/plans')
		// 			return
		// 		}

		// 		// If payment update is required (past_due status), redirect to billing page
		// 		if (result.paymentUpdateRequired) {
		// 			router.push('/billing')
		// 			return
		// 		}

		// 		// If no active subscription, redirect to plans page
		// 		if (!result.hasSubscription) {
		// 			router.push('/plans')
		// 			return
		// 		}

		// 		setLoading(false)
		// 	} catch (err) {
		// 		console.error('Error checking subscription:', err)
		// 		setLoading(false)
		// 	}
		// }
		setLoading(false)
		// if (isLoggedIn && user) {
		// 	// checkSubscription()
		// } else {
			
		// }
	}, [isLoggedIn, router, loading])

	if (loading) {
		return (
			<div className="flex h-screen w-full items-center justify-center">
				<div className="w-10 h-10 bg-primary rounded-full animate-pulse" />
			</div>
		)
	}

	return <>{children}</>
}
