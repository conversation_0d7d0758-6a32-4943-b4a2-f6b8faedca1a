'use client'

import { useUserStore } from '@/store/userStore'
import { useEffect, useState, FormEvent } from 'react'
import { useRouter } from 'next/navigation'
import { createProject } from '@/actions/project'
import { fetchWebsiteContent } from '@/actions/website'
import { hasActiveSubscription } from '@/actions/subscription'

export default function StartProjectPage() {
	const { user, isLoggedIn } = useUserStore() as { user: any; isLoggedIn: boolean }
	const router = useRouter()
	// const [loading, setLoading] = useState(true)
	const [submitting, setSubmitting] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [fetchingWebsite, setFetchingWebsite] = useState(false)

	// Form state
	const [name, setName] = useState('')
	const [url, setUrl] = useState('')
	const [description, setDescription] = useState('')
	const [painPoint, setPainPoint] = useState('')
	const [businessType, setBusinessType] = useState('B2B')
	const [keywords, setKeywords] = useState<string[]>([])
	const [keywordInput, setKeywordInput] = useState('')
	const [primaryKeyword, setPrimaryKeyword] = useState('')
	const [averageSaleValue, setAverageSaleValue] = useState<number | ''>('')

	// useEffect(() => {
	// 	// Check if user is logged in
	// 	if (!isLoggedIn && !loading) {
	// 		router.push('/login')
	// 		return
	// 	}

	// 	// Check if user has an active subscription
	// 	const checkSubscription = async () => {
	// 		try {
	// 			const result = await hasActiveSubscription(user.uid)

	// 			if (!result.success) {
	// 				// Handle error
	// 				router.push('/plans')
	// 				return
	// 			}

	// 			// If payment update is required (past_due status), redirect to billing page
	// 			if (result.paymentUpdateRequired) {
	// 				router.push('/billing')
	// 				return
	// 			}

	// 			// If no active subscription, redirect to plans page
	// 			if (!result.hasSubscription) {
	// 				router.push('/plans')
	// 				return
	// 			}

	// 			setLoading(false)
	// 		} catch (err) {
	// 			console.error('Error checking subscription:', err)
	// 			setLoading(false)
	// 		}
	// 	}

	// 	if (isLoggedIn && user) {
	// 		checkSubscription()
	// 	} else {
	// 		setLoading(false)
	// 	}
	// }, [isLoggedIn, router, loading, user])

	const handleFetchWebsite = async () => {
		if (!url) return

		setFetchingWebsite(true)
		setError(null)

		try {
			const result = await fetchWebsiteContent(url)

			if (result.success) {
				if (result.description) {
					setDescription(result.description)
				}

				if (result.painPoint) {
					setPainPoint(result.painPoint)
				}

				if (result.keywords && result.keywords.length > 0) {
					setKeywords(result.keywords)
				}

				if (result.primaryKeyword) {
					setPrimaryKeyword(result.primaryKeyword)
				}
			} else {
				setError(result.error || 'Failed to fetch website content')
			}
		} catch (err) {
			console.error('Error fetching website content:', err)
			setError('An unexpected error occurred while fetching website content')
		} finally {
			setFetchingWebsite(false)
		}
	}

	const handleSubmit = async (e: FormEvent) => {
		e.preventDefault()

		// Validate form
		if (!name || !url || !description || !painPoint || !averageSaleValue) {
			setError('Please fill in all required fields')
			return
		}

		setSubmitting(true)
		setError(null)

		try {
			// Create project
			const result = await createProject({
				userId: user.uid,
				name,
				url,
				description,
				painPoint,
				businessType,
				averageSaleValue: Number(averageSaleValue),
				keywords: keywords.join(','),
				primaryKeyword,
				aiEnabled: true, // Keep AI enabled by default
			})

			if (result.success) {
				// Navigate to subreddits page
				router.push(`/project/${result.id}/subreddits`)
			} else {
				setError('Failed to create project. Please try again.')
			}
		} catch (err) {
			console.error('Error creating project:', err)
			setError('An unexpected error occurred. Please try again.')
		} finally {
			setSubmitting(false)
		}
	}

	// if (loading) {
	// 	return (
	// 		<div className="flex justify-center items-center min-h-screen">
	// 			<div className="loading loading-spinner loading-lg"></div>
	// 		</div>
	// 	)
	// }

	return (
		<div className="container mx-auto px-4 py-8 max-w-3xl">
			<h1 className="text-3xl font-bold text-center text-primary mb-2">Tell Us About Your Product</h1>
			<p className="text-center text-gray-600 mb-8">
				This information helps us find the perfect leads for your business
			</p>
			<p className="text-center text-sm text-gray-500 mb-6">
				Fields marked with <span className="text-red-500">*</span> are required
			</p>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<form className="space-y-8" onSubmit={handleSubmit}>
						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
										/>
									</svg>
									Product Name <span className="text-red-500 ml-1">*</span>
								</span>
							</label>
							<input
								type="text"
								className="input input-bordered w-full focus:input-primary"
								placeholder="Enter product name"
								value={name}
								onChange={(e) => setName(e.target.value)}
								required
							/>
						</div>

						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
									Product URL <span className="text-red-500 ml-1">*</span>
								</span>
							</label>
							<div className="flex items-center gap-2">
								<input
									type="url"
									className="input input-bordered w-full focus:input-primary"
									placeholder="https:// yourwebsite.com"
									value={url}
									onChange={(e) => setUrl(e.target.value)}
									required
								/>
								<button
									type="button"
									className="btn btn-square !rounded btn-primary"
									onClick={handleFetchWebsite}
									disabled={fetchingWebsite || !url}
								>
									{fetchingWebsite ? (
										<span className="loading loading-spinner loading-xs"></span>
									) : (
										<svg
											xmlns="http://www.w3.org/2000/svg"
											className="h-5 w-5"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
											/>
										</svg>
									)}
								</button>
							</div>
							<label className="label">
								<span className="label-text-alt text-gray-500">Enter your product's website URL</span>
							</label>
						</div>
						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M4 6h16M4 12h16M4 18h7"
										/>
									</svg>
									Product Description <span className="text-red-500 ml-1">*</span>
								</span>
							</label>
							<div className="relative">
								<textarea
									className="textarea textarea-bordered w-full h-32 focus:textarea-primary"
									placeholder="Enter a short product description"
									value={description}
									onChange={(e) => setDescription(e.target.value)}
									maxLength={500}
									required
								></textarea>
								<div className="absolute bottom-2 right-2 text-xs text-gray-500">
									{description.length}/500
								</div>
							</div>
							<label className="label">
								<span className="label-text-alt text-gray-500">
									What your product does and who it's for
								</span>
							</label>
						</div>

						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
										/>
									</svg>
									Product Pain Point <span className="text-red-500 ml-1">*</span>
								</span>
							</label>
							<div className="relative">
								<textarea
									className="textarea textarea-bordered w-full h-32 focus:textarea-primary"
									placeholder="Example: 'Our customers struggle to find high-quality leads on Reddit...'"
									value={painPoint}
									onChange={(e) => setPainPoint(e.target.value)}
									maxLength={500}
									required
								></textarea>
								<div className="absolute bottom-2 right-2 text-xs text-gray-500">
									{painPoint.length}/500
								</div>
							</div>
							<label className="label">
								<span className="label-text-alt text-gray-500">
									What problem does your product solve?
								</span>
							</label>
						</div>

						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
										/>
									</svg>
									Keywords
								</span>
							</label>
							<div className="flex flex-wrap gap-2 mb-2">
								{keywords.map((keyword, index) => (
									<div key={index} className="badge badge-primary gap-1">
										{keyword}
										<button
											type="button"
											onClick={() => setKeywords(keywords.filter((_, i) => i !== index))}
											className="btn btn-xs btn-circle btn-ghost"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												className="h-3 w-3"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth={2}
													d="M6 18L18 6M6 6l12 12"
												/>
											</svg>
										</button>
									</div>
								))}
							</div>
							<div className="flex items-center gap-2">
								<input
									type="text"
									className="input input-bordered w-full focus:input-primary"
									placeholder="Type keywords and press Enter, comma, or period to add"
									value={keywordInput}
									onChange={(e) => setKeywordInput(e.target.value)}
									onKeyDown={(e) => {
										if (e.key === 'Enter' || e.key === ',' || e.key === '.') {
											e.preventDefault()
											if (keywordInput.trim()) {
												setKeywords([...keywords, keywordInput.trim()])
												setKeywordInput('')
											}
										}
									}}
									onBlur={() => {
										if (keywordInput.trim()) {
											setKeywords([...keywords, keywordInput.trim()])
											setKeywordInput('')
										}
									}}
								/>
							</div>
							<label className="label">
								<span className="label-text-alt text-gray-500">
									Enter keywords related to your product to help find relevant leads
								</span>
							</label>
						</div>

						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
										/>
									</svg>
									Primary Keyword
								</span>
							</label>
							<input
								type="text"
								className="input input-bordered w-full focus:input-primary"
								placeholder="Enter your main keyword"
								value={primaryKeyword}
								onChange={(e) => setPrimaryKeyword(e.target.value)}
							/>
							<label className="label">
								<span className="label-text-alt text-gray-500">
									The main keyword that best describes your product
								</span>
							</label>
						</div>

						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
										/>
									</svg>
									Business Type <span className="text-red-500 ml-1">*</span>
								</span>
							</label>
							<div className="grid grid-cols-2 gap-4 mt-2">
								<div
									className={`border rounded-lg p-4 cursor-pointer transition-colors ${businessType === 'B2B' ? 'border-primary bg-primary/10' : 'border-gray-300'}`}
									onClick={() => setBusinessType('B2B')}
								>
									<div className="flex items-center gap-2 mb-2">
										<input
											type="radio"
											name="businessType"
											className="radio radio-primary"
											checked={businessType === 'B2B'}
											readOnly
										/>
										<span className="font-medium">B2B</span>
									</div>
									<p className="text-sm text-gray-600">Business to Business</p>
								</div>
								<div
									className={`border rounded-lg p-4 cursor-pointer transition-colors ${businessType === 'B2C' ? 'border-primary bg-primary/10' : 'border-gray-300'}`}
									onClick={() => setBusinessType('B2C')}
								>
									<div className="flex items-center gap-2 mb-2">
										<input
											type="radio"
											name="businessType"
											className="radio radio-primary"
											checked={businessType === 'B2C'}
											readOnly
										/>
										<span className="font-medium">B2C</span>
									</div>
									<p className="text-sm text-gray-600">Business to Consumer</p>
								</div>
							</div>
						</div>

						<div className="form-control">
							<label className="label">
								<span className="label-text flex items-center gap-2">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 text-primary"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
									Average Sale Value <span className="text-red-500 ml-1">*</span>
								</span>
							</label>
							<div className="relative">
								<span className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
									$
								</span>
								<input
									type="number"
									className="input input-bordered w-full pl-8 focus:input-primary"
									placeholder="Enter the dollar value of a typical sale"
									value={averageSaleValue}
									onChange={(e) =>
										setAverageSaleValue(e.target.value === '' ? '' : Number(e.target.value))
									}
									required
								/>
							</div>
							<label className="label">
								<span className="label-text-alt text-gray-500">
									How much is a typical sale worth to you? ($)
								</span>
							</label>
						</div>

						<div className="form-control mt-8">
							<button
								type="submit"
								className="btn btn-primary w-full text-white gap-2"
								disabled={submitting}
							>
								{submitting ? (
									<>
										<span className="loading loading-spinner loading-sm"></span>
										Creating Project...
									</>
								) : (
									<>
										Continue to Subreddit Selection
										<svg
											xmlns="http://www.w3.org/2000/svg"
											className="h-5 w-5"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M14 5l7 7m0 0l-7 7m7-7H3"
											/>
										</svg>
									</>
								)}
							</button>

							{error && (
								<div className="alert alert-error mt-4">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="stroke-current shrink-0 h-6 w-6"
										fill="none"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth="2"
											d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
									<span>{error}</span>
								</div>
							)}
						</div>
					</form>
				</div>
			</div>
		</div>
	)
}
