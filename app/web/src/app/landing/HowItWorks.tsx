import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'

interface StepCardProps {
	number: number
	title: string
	description: string
	borderColor: string
	circleColor: string
	image: string
}

const StepCard = ({ number, title, description, borderColor, circleColor, image }: StepCardProps) => {
	return (
		<Card className={`bg-white rounded-xl shadow-md border-4 ${borderColor} relative`}>
			<CardContent className="p-6 flex flex-col items-center">
				<div
					className={`w-10 h-10 ${circleColor} rounded-full flex items-center justify-center text-white text-lg font-bold absolute -top-5 -left-5 border-4 border-white shadow-md`}
				>
					{number}
				</div>
				<div className="relative w-full h-32 mb-4 mt-2">
					<Image src={image} alt="Step demo" className="w-full h-full object-contain " fill />
				</div>
				<h3 className="text-xl font-bold mb-3 mt-2 text-center">{title}</h3>
				<p className="text-gray-600 text-center">{description}</p>
			</CardContent>
		</Card>
	)
}

const HowItWorks = () => {
	const steps = [
		{
			number: 1,
			title: 'Add your project',
			description: 'Create a new project by entering your URL and a short description of your project.',
			borderColor: 'border-[#FF7A7A]',
			circleColor: 'bg-[#FF7A7A]',
			image: require('./assets/demo-step1.png'),
		},
		{
			number: 2,
			title: 'We identify the best subreddits',
			description: "We identify the best subreddits to target based on your project's goal and audience.",
			borderColor: 'border-[#FFA940]',
			circleColor: 'bg-[#FFA940]',
			image: require('./assets/demo-step2.png'),
		},
		{
			number: 3,
			title: 'Engage with potential customers',
			description: 'We present you a list of relevant threads and the perfect answer to engage with them.',
			borderColor: 'border-[#3DD598]',
			circleColor: 'bg-[#3DD598]',
			image: require('./assets/demo-step3.png'),
		},
	]

	return (
		<section id="how-it-works" className="py-16 bg-[#fff8f5]">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-12">
					<h2 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-2">
						How it works{' '}
						<span role="img" aria-label="rocket">
							🚀
						</span>{' '}
						?
					</h2>
				</div>
				<div className="flex flex-col md:flex-row items-center justify-center gap-8 relative">
					{steps.map((step, index) => (
						<div key={index} className="relative flex flex-col items-center w-full md:w-1/3">
							<StepCard
								number={step.number}
								title={step.title}
								description={step.description}
								borderColor={step.borderColor}
								circleColor={step.circleColor}
								image={step.image}
							/>
						</div>
					))}
				</div>
			</div>
		</section>
	)
}

export default HowItWorks
