import { Button } from '@/components/ui/button'
import { ArrowRight } from 'lucide-react'
import Link from 'next/link'
const FinalPromo = () => {
	return (
		<section className="py-16">
			<div className="max-w-6xl mx-auto px-4">
				<div className="bg-[#FF4500] rounded-3xl py-16 px-8 text-center relative overflow-hidden">
					{/* Background pattern elements */}
					<div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-[#FF4500] to-[#ff6a33] opacity-70"></div>
					<div className="absolute -top-12 -left-12 w-64 h-64 rounded-full bg-[#ff6a33] opacity-20"></div>
					<div className="absolute -bottom-16 -right-16 w-64 h-64 rounded-full bg-[#ff6a33] opacity-20"></div>

					{/* Content */}
					<div className="relative z-10">
						<span className="inline-block px-4 py-1 bg-white text-[#FF4500] rounded-full text-sm font-medium mb-6">
							LET'S TRY!
						</span>
						<h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
							Start growing 
							<br />
							on Reddit <span className="text-yellow-300">today!</span>
						</h2>
						<div className="max-w-md mx-auto">
							<Link href="/signup">
								<Button className="bg-white text-[#FF4500] hover:bg-gray-100 hover:text-[#FF4500] text-lg px-8 py-6">
									Get Started now <ArrowRight className="ml-2 h-5 w-5" />
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</div>
		</section>
	)
}

export default FinalPromo
