export const dynamic = 'force-dynamic'
import { type NextRequest, NextResponse } from 'next/server'
import { verifyWebhookSignature } from '@/services/stripe'
import { db, schema } from '@packages/shared'
import { and, desc, eq, isNull } from 'drizzle-orm'
import { connection } from 'next/server'

export async function POST(request: NextRequest) {
	try {
		// Get the raw request body
		await connection()
		const rawBody = await request.text()

		// Get the signature from the headers
		const signature = request.headers.get('stripe-signature') || ''

		// Verify the signature
		const result = await verifyWebhookSignature(signature, rawBody)

		if (!result.success) {
			return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
		}

		const event = result.event

		// Handle different event types
		switch (event?.type) {
			case 'checkout.session.completed':
				await handleCheckoutSessionCompleted(event.data.object)
				break
			case 'customer.subscription.created':
				await handleSubscriptionCreated(event.data.object)
				break
			case 'customer.subscription.updated':
				await handleSubscriptionUpdated(event.data.object)
				break
			case 'customer.subscription.deleted':
				await handleSubscriptionDeleted(event.data.object)
				break
			case 'invoice.payment_succeeded':
				await handleInvoicePaymentSucceeded(event.data.object)
				break
			case 'invoice.payment_failed':
				await handleInvoicePaymentFailed(event.data.object)
				break
			default:
				console.log(`Unhandled event type: ${event?.type}`)
		}

		return NextResponse.json({ success: true })
	} catch (error) {
		console.error('Error handling webhook:', error)
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
	}
}

/**
 * Handle checkout.session.completed event
 */
async function handleCheckoutSessionCompleted(session: any) {
	try {
		// Get the customer ID and metadata
		const customerId = session.customer
		const metadata = session.metadata || {}
		const userId = metadata.userId

		if (!userId) {
			console.error('No user ID in metadata')
			return
		}

		// Find the pending subscription
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(and(eq(schema.subscription.userId, userId), isNull(schema.subscription.stripeSubscriptionId)))
			.limit(1)
			.orderBy(desc(schema.subscription.createdAt))

		if (subscriptions.length === 0) {
			console.error(`No pending subscription found for user ${userId}`)
			return
		}

		// Update the subscription with the checkout session ID
		await db
			.update(schema.subscription)
			.set({
				// status: 'active',
				updatedAt: new Date(),
			})
			.where(eq(schema.subscription.id, subscriptions[0].id))
	} catch (error) {
		console.error('Error handling checkout.session.completed:', error)
	}
}

/**
 * Handle customer.subscription.created event
 */
async function handleSubscriptionCreated(subscription: any) {
	try {
		// Get the customer ID and metadata
		const customerId = subscription.customer
		const metadata = subscription.metadata || {}
		const userId = metadata.userId

		if (!userId) {
			console.error('No user ID in metadata')
			return
		}

		// Find the pending subscription
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(and(eq(schema.subscription.userId, userId), eq(schema.subscription.stripeCustomerId, customerId), isNull(schema.subscription.stripeSubscriptionId)))
			.limit(1)

		if (subscriptions.length === 0) {
			console.error(`No pending subscription found for user ${userId}`)
			return
		}

		// Calculate trial end date
		const trialEndDate = subscription.trial_end ? new Date(subscription.trial_end * 1000) : null

		// Update the subscription
		await db
			.update(schema.subscription)
			.set({
				stripeSubscriptionId: subscription.id,
				status: subscription.status,
				isTrialActive: subscription.status === 'trialing',
				trialEndDate,
				updatedAt: new Date(),
			})
			.where(eq(schema.subscription.id, subscriptions[0].id))
	} catch (error) {
		console.error('Error handling customer.subscription.created:', error)
	}
}

/**
 * Handle customer.subscription.updated event
 */
async function handleSubscriptionUpdated(subscription: any) {
	try {
		// Find the subscription by Stripe subscription ID
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(eq(schema.subscription.stripeSubscriptionId, subscription.id))
			.limit(1)

		if (subscriptions.length === 0) {
			console.error(`No subscription found with Stripe ID ${subscription.id}`)
			return
		}

		// Calculate trial end date
		const trialEndDate = subscription.trial_end ? new Date(subscription.trial_end * 1000) : null

		// Update the subscription
		await db
			.update(schema.subscription)
			.set({
				status: subscription.status,
				isTrialActive: subscription.status === 'trialing',
				trialEndDate,
				updatedAt: new Date(),
			})
			.where(eq(schema.subscription.id, subscriptions[0].id))
	} catch (error) {
		console.error('Error handling customer.subscription.updated:', error)
	}
}

/**
 * Handle customer.subscription.deleted event
 */
async function handleSubscriptionDeleted(subscription: any) {
	try {
		// Find the subscription by Stripe subscription ID
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(eq(schema.subscription.stripeSubscriptionId, subscription.id))
			.limit(1)

		if (subscriptions.length === 0) {
			console.error(`No subscription found with Stripe ID ${subscription.id}`)
			return
		}

		// Update the subscription
		await db
			.update(schema.subscription)
			.set({
				status: 'canceled',
				isTrialActive: false,
				canceledAt: new Date(),
				updatedAt: new Date(),
			})
			.where(eq(schema.subscription.id, subscriptions[0].id))
	} catch (error) {
		console.error('Error handling customer.subscription.deleted:', error)
	}
}

/**
 * Handle invoice.payment_succeeded event
 */
async function handleInvoicePaymentSucceeded(invoice: any) {
	try {
		// Only process subscription invoices
		if (!invoice.subscription) {
			return
		}

		// Find the subscription by Stripe subscription ID
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(eq(schema.subscription.stripeSubscriptionId, invoice.subscription))
			.limit(1)

		if (subscriptions.length === 0) {
			console.error(`No subscription found with Stripe ID ${invoice.subscription}`)
			return
		}

		// Update the subscription
		await db
			.update(schema.subscription)
			.set({
				status: 'active',
				updatedAt: new Date(),
			})
			.where(eq(schema.subscription.id, subscriptions[0].id))
	} catch (error) {
		console.error('Error handling invoice.payment_succeeded:', error)
	}
}

/**
 * Handle invoice.payment_failed event
 */
async function handleInvoicePaymentFailed(invoice: any) {
	try {
		// Only process subscription invoices
		if (!invoice.subscription) {
			return
		}

		// Find the subscription by Stripe subscription ID
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(eq(schema.subscription.stripeSubscriptionId, invoice.subscription))
			.limit(1)

		if (subscriptions.length === 0) {
			console.error(`No subscription found with Stripe ID ${invoice.subscription}`)
			return
		}

		// Update the subscription
		await db
			.update(schema.subscription)
			.set({
				status: 'past_due',
				updatedAt: new Date(),
			})
			.where(eq(schema.subscription.id, subscriptions[0].id))
	} catch (error) {
		console.error('Error handling invoice.payment_failed:', error)
	}
}
