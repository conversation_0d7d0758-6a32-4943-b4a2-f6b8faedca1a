'use client'

import { auth } from '@/utils/firebase'
import { signOut } from 'firebase/auth'
import Cookies from 'js-cookie'
import { env } from 'next-runtime-env'

export async function logout() {
	try {
		// Sign out from Firebase
		await signOut(auth)

		// Remove token cookie
		Cookies.remove('token', {
			path: '/',
			domain: env('NEXT_PUBLIC_DOMAIN') || undefined,
		})

		return { success: true }
	} catch (error) {
		console.error('Logout error:', error)
		return { success: false, error }
	}
}
