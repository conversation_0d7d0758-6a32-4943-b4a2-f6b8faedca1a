import { ReactNode } from 'react'

interface FloatingCardProps {
	className?: string
	icon: ReactNode
	text: string
}

const FloatingCard = ({ className, icon, text }: FloatingCardProps) => {
	return (
		<div className={`floating-card bg-white rounded-lg p-3 shadow-lg ${className}`}>
			<div className="flex items-center text-sm">
				<div className="rounded-full bg-gray-100 p-2 mr-2">{icon}</div>
				<div className="text-gray-700">{text}</div>
			</div>
		</div>
	)
}

export default FloatingCard
