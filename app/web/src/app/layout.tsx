import type { Metadata } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from 'next/font/google'
import './globals.css'
import FirebaseHelper from './FirebaseHelper'
import Navigation from '@/components/Navigation'
import { PublicEnvScript } from 'next-runtime-env'
import Head from 'next/head'
import Script from 'next/script'
const geistSans = Geist({
	variable: '--font-geist-sans',
	subsets: ['latin'],
})

const geistMono = Geist_Mono({
	variable: '--font-geist-mono',
	subsets: ['latin'],
})

export const metadata: Metadata = {
	title: 'LeadsAutomatic - Find your customers on Reddit',
	description: 'LeadsAutomatic is a tool that helps you find your customers on Reddit.',
}

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode
}>) {
	return (
		<html lang="en" data-theme="automatic">
			<head>
				<PublicEnvScript />
				<link rel="icon" href="/favicon.png" />
			</head>

			<body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
				<FirebaseHelper />
				<Navigation />
				{children}
				<Script>
					{`
						(function(v,i,s,a,t){v[t]=v[t]||function(){(v[t].v=v[t].v||[]).push(arguments)};if(!v._visaSettings){v._visaSettings={}}v._visaSettings[a]={v:'1.0',s:a,a:'1',t:t};var b=i.getElementsByTagName('body')[0];var p=i.createElement('script');p.defer=1;p.async=1;p.src=s+'?s='+a;b.appendChild(p)})(window,document,'//app-worker.visitor-analytics.io/main.js','9093f1df-2f4c-11f0-92f6-960004340fd3','va')
					`}
				</Script>
			</body>
		</html>
	)
}
