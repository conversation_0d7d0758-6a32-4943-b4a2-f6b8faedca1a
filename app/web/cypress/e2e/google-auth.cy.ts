describe('Google Authentication', () => {
	beforeEach(() => {
		// Clear cookies and localStorage before each test
		cy.clearCookies()
		cy.clearLocalStorage()
		cy.visit('/')
	})

	it('should have Google login button on login page', () => {
		cy.visit('/login')
		cy.contains('Login with <PERSON>').should('be.visible')
	})

	it('should have Google signup button on signup page', () => {
		cy.visit('/signup')
		cy.contains('Sign up with Google').should('be.visible')
	})

	// Note: Testing actual Google authentication flow is challenging in Cypress
	// because it involves third-party authentication.
	// The following test is a placeholder and would require additional setup
	// to work properly in a real environment.
	it.skip('should handle Google authentication', () => {
		// This test is skipped because it requires special setup
		// to handle the Google OAuth flow
		cy.visit('/login')
		cy.window().then((win) => {
			// Mock the Google auth response
			;(win as any).firebase = {
				auth: () => ({
					signInWithPopup: cy.stub().resolves({
						user: {
							uid: 'google-uid',
							email: '<EMAIL>',
							displayName: 'Google User',
							photoURL: 'https://example.com/photo.jpg',
							getIdToken: cy.stub().resolves('fake-google-token'),
							getIdTokenResult: cy.stub().resolves({
								claims: { dbId: 'google-db-id' },
								expirationTime: new Date(Date.now() + 3600000).toISOString(),
							}),
						},
					}),
					GoogleAuthProvider: class {
						// Mock provider
					},
					onAuthStateChanged: cy.stub(),
					onIdTokenChanged: cy.stub(),
				}),
			}
		})

		cy.contains('Login with Google').click()

		// Since we're mocking, we need to manually navigate
		cy.visit('/project/new')
		cy.url().should('include', '/project/new')
	})
})
