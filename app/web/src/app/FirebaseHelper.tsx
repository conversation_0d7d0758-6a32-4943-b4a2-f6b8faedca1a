'use client'
import { auth } from '@/utils/firebase'
import type { User } from 'firebase/auth'
import { useUserStore } from '@/store/userStore'
import { useEffect } from 'react'
import { env } from 'next-runtime-env'
import Cookies from 'js-cookie'
import { createUser } from '@/actions/user'

const handleToken = async (user: User | null) => {
	if (user) {
		const token = await user.getIdToken()
		const decodedToken = await user.getIdTokenResult()
		const expirationTime = Math.floor((new Date(decodedToken.expirationTime).getTime() - Date.now()) / 1000)
		Cookies.set('token', token, {
			domain: env('NEXT_PUBLIC_DOMAIN') || undefined,
			expires: expirationTime / (60 * 60 * 24), // Convert seconds to days
			path: '/',
		})

		// Create or update user in database
		try {
			await createUser({
				firebaseUid: user.uid,
				email: user.email || '',
			})

			// We'll check subscription status in the page components instead of setting a cookie
		} catch (error) {
			console.error('Error creating/updating user:', error)
		}
	} else {
		Cookies.remove('token', {
			path: '/',
			domain: env('NEXT_PUBLIC_DOMAIN') || undefined,
		})
	}
}

const FirebaseHelper = () => {
	const updateUser = useUserStore((state: any) => state.updateUser)
	const isLoggedIn = useUserStore((state: any) => state.isLoggedIn)
	const updateLoading = useUserStore((state: any) => state.updateLoading)
	// const { mutateAsync: assignDraft } = useMutationClient(['assignDraft'], '/api/property/assign-draft', 'POST')
	useEffect(() => {
		if (!env('NEXT_PUBLIC_FIREBASE_API_KEY')) {
			return
		}
		// Skip if auth is not available (server-side)
		if (!auth) return () => {}

		const unsubscribe = auth.onIdTokenChanged(handleToken)
		const unsubscribe2 = auth.onAuthStateChanged(async (user: any) => {
			handleToken(user)
			if (user) {
				const token = await user.getIdTokenResult()
				;(user as any).dbId = token.claims.dbId
			}
			updateUser(user || null)
			updateLoading(false)
			// if (user && localStorage.getItem('draftId')) {
			// 	const n = Number.parseInt(localStorage.getItem('draftPropertyId') || '0')
			// 	if (n) {
			// 		try {
			// 			await assignDraft({
			// 				propertyId: n,
			// 				draftId: localStorage.getItem('draftId') || '',
			// 			})
			// 			localStorage.removeItem('draftId')
			// 			localStorage.removeItem('draftPropertyId')
			// 		} catch (error) {
			// 			console.error(error)
			// 		}
			// 	}
			// }
		})
		return () => {
			unsubscribe()
			unsubscribe2()
		}
	}, [updateUser, updateLoading])
	return <>{isLoggedIn && <RefreshToken />}</>
}

const RefreshToken = () => {
	useEffect(() => {
		// Skip if auth is not available (server-side)
		if (!auth) return

		const interval = 2 * 60 * 1000
		const handle = setInterval(async () => {
			// Use optional chaining to safely access currentUser
			if (auth?.currentUser) {
				await auth.currentUser.getIdToken(true)
			}
		}, interval)
		return () => clearInterval(handle)
	}, [])
	return null
}

export default FirebaseHelper
