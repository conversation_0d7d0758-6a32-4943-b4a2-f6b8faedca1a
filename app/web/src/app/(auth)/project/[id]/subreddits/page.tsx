'use client'

import { useUserStore } from '@/store/userStore'
import { useEffect, useState, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { aiSuggestSubreddits, getProjectById } from '@/actions/project'
import { searchSubreddits, addSubredditToProject } from '@/actions/subreddit'
import { checkSubscriptionStatus } from '@/utils/subscription-check'
import Select from 'react-select'
import debounce from 'lodash/debounce'
import { useParams } from 'next/navigation'
import Link from 'next/link'

export default function ProjectSubredditsPage() {
	const { isLoggedIn } = useUserStore() as { user: any; isLoggedIn: boolean }
	const params = useParams<{ id: string }>()
	const router = useRouter()
	const [loading, setLoading] = useState(true)
	const [project, setProject] = useState<any>(null)
	const [error, setError] = useState<string | null>(null)
	const [subreddits, setSubreddits] = useState<any[]>([])
	const [searchQuery, setSearchQuery] = useState('')
	const [searchResults, setSearchResults] = useState<any[]>([])
	const [isSearching, setIsSearching] = useState(false)
	const selectedCount = useMemo(
		() => subreddits.reduce((acc, subreddit) => (subreddit.selected ? acc + 1 : acc), 0),
		[subreddits],
	)

	// Debounced search function
	const debouncedSearch = useCallback(
		debounce(async (query: string) => {
			if (query.trim().length < 2) {
				setSearchResults([])
				setIsSearching(false)
				return
			}

			try {
				const result = await searchSubreddits(query)
				if (result.success) {
					setSearchResults(result.data || [])
				}
			} catch (error) {
				console.error('Error searching subreddits:', error)
			} finally {
				setIsSearching(false)
			}
		}, 300),
		[],
	)

	// Handle search input change
	const handleSearchChange = (inputValue: string) => {
		setSearchQuery(inputValue)
		if (inputValue.trim().length >= 2) {
			setIsSearching(true)
			debouncedSearch(inputValue)
		} else {
			setSearchResults([])
		}
	}

	// Handle selecting a subreddit from search results
	const handleSelectSubreddit = async (option: any) => {
		if (!option || !params.id) return

		try {
			const temp = [...subreddits]
			const exist = temp.findIndex((subreddit) => subreddit.id === option.data.id)
			if (exist !== -1) {
				temp[exist].selected = selectedCount < 10
			} else {
				temp.push({
					...option.data,
					selected: selectedCount < 10,
					aiSuggested: false,
				})
			}
			addSubredditToProject(params.id, option.data.id, selectedCount < 10)
			setSubreddits(temp)
		} catch (error) {
			console.error('Error adding subreddit:', error)
		}
	}

	useEffect(() => {
		// Check if user is logged in
		if (!isLoggedIn && !loading) {
			router.push('/login')
			return
		}

		// Fetch project data
		const fetchProject = async () => {
			try {
				// Check subscription status first
				const { user } = useUserStore.getState() as { user: any }
				if (!user?.uid) {
					router.push('/login')
					return
				}

				const [result, subreddits] = await Promise.all([
					getProjectById(params.id),
					aiSuggestSubreddits(params.id),
				])
				if (subreddits.success && subreddits.data) {
					setSubreddits(subreddits.data)
				}
				if (result.success && result.project) {
					setProject(result.project)
				} else {
					setError('Project not found')
				}
			} catch (err) {
				console.error('Error fetching project:', err)
				setError('Failed to load project data')
			} finally {
				setLoading(false)
			}
		}

		if (isLoggedIn) {
			fetchProject()
		}
	}, [isLoggedIn, params.id, router, loading])

	const handleSubredditToggle = async (id: string) => {
		try {
			// Toggle in UI first for immediate feedback
			const temp = [...subreddits]
			const index = temp.findIndex((subreddit) => subreddit.id === id)
			addSubredditToProject(params.id, id, !temp[index].selected)
			temp[index].selected = !temp[index].selected
			setSubreddits(temp)

			// Update in database
		} catch (error) {
			console.error('Error toggling subreddit:', error)
		}
	}

	const customStyles = {
		control: (provided: any) => ({
			...provided,
			borderRadius: '0.5rem',
			border: '1px solid hsl(var(--bc) / 0.2)',
			padding: '0.25rem 0.5rem',
			paddingLeft: '2.5rem',
			boxShadow: 'none',
			backgroundColor: 'transparent',
			'&:hover': {
				borderColor: 'hsl(var(--p))',
			},
		}),
		placeholder: (provided: any) => ({
			...provided,
			color: 'hsl(var(--bc) / 0.5)',
		}),
		input: (provided: any) => ({
			...provided,
			color: 'hsl(var(--bc))',
		}),
		option: (provided: any, state: any) => ({
			...provided,
			backgroundColor: state.isSelected
				? 'hsl(var(--p))'
				: state.isFocused
					? 'hsl(var(--p) / 0.2)'
					: 'transparent',
			color: state.isSelected ? 'hsl(var(--pc))' : 'hsl(var(--bc))',
			'&:hover': {
				backgroundColor: 'hsl(var(--p) / 0.2)',
			},
		}),
	}

	if (loading) {
		return (
			<div className="flex justify-center items-center min-h-screen">
				<div className="loading loading-spinner loading-lg" />
			</div>
		)
	}

	if (error) {
		return (
			<div className="container mx-auto px-4 py-8 max-w-3xl">
				<div className="alert alert-error">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="stroke-current shrink-0 h-6 w-6"
						fill="none"
						viewBox="0 0 24 24"
					>
						<title>Error</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth="2"
							d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
						/>
					</svg>
					<span>{error}</span>
				</div>
			</div>
		)
	}

	return (
		<div className="container mx-auto px-4 py-8 max-w-3xl">
			<h1 className="text-3xl font-bold text-center text-primary mb-2">Find Your Audience</h1>
			<p className="text-center text-gray-600 mb-4">
				Choose subreddits that align with your product. You can select up to 10.
			</p>
			<div className="flex items-center justify-center mb-6">
				<div className="flex items-center">
					<div className="bg-primary bg-opacity-20 p-1 rounded-full mr-2">
						<span className="text-primary text-sm">👥</span>
					</div>
					<span className="text-sm">
						Targeted communities will help us find the most relevant leads for{' '}
						<span className="text-primary font-medium">{project?.name || 'Videotree'}</span>
					</span>
				</div>
			</div>

			<div className="flex justify-between items-center mb-4">
				<div className="badge badge-outline p-3">Selected: {selectedCount}/10 subreddits</div>
				<Link href={`/project/${params.id}`} className="btn btn-primary btn-sm">
					Scan for Leads
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="h-4 w-4 ml-1"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<title>Scan for Leads</title>
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
					</svg>
				</Link>
			</div>

			<div className="relative mb-6">
				<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
					<svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<title>Search</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
						/>
					</svg>
				</div>
				<Select
					styles={customStyles}
					classNames={{
						container: () => 'border rounded-md',
					}}
					placeholder="Search subreddits by name or description..."
					options={searchResults.map((result) => ({
						value: result.id,
						label: result.prefixedName,
						data: result,
					}))}
					onInputChange={handleSearchChange}
					onChange={handleSelectSubreddit}
					isLoading={isSearching}
					noOptionsMessage={({ inputValue }) =>
						inputValue.trim().length < 2 ? 'Type at least 2 characters to search' : 'No subreddits found'
					}
					isClearable
					className="w-full"
				/>
			</div>

			<div className="card bg-base-100 shadow-sm mb-6 border border-base-200">
				<div className="card-body p-4">
					<div className="flex justify-between items-center mb-2">
						<div className="flex items-center">
							<svg
								className="h-5 w-5 text-primary mr-2"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<title>Relevent Subreddits</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
								/>
							</svg>
							<h3 className="font-medium text-primary">Relevent Subreddits</h3>
						</div>
						<span className="text-sm text-gray-500">{subreddits.length} subreddits</span>
					</div>

					<div className="space-y-3 mt-2">
						{subreddits.map((subreddit) => (
							<div key={subreddit.id} className="border-b border-base-200 pb-3 last:border-0 last:pb-0">
								<div className="flex items-center mb-1">
									<input
										type="checkbox"
										className="checkbox checkbox-primary checkbox-sm mr-3"
										checked={subreddit.selected}
										onChange={() => handleSubredditToggle(subreddit.id)}
									/>
									<span className="font-medium">{subreddit.prefixedName}</span>
									<span className="text-xs text-gray-500 ml-2 flex items-center">
										<svg
											className="h-3 w-3 mr-1"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Subscribers</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
											/>
										</svg>
										{subreddit.subscribers} members
									</span>
								</div>
								<p className="text-sm text-gray-600 ml-8 line-clamp-2 overflow-hidden">
									{subreddit.publicDescription}
								</p>
							</div>
						))}
					</div>
				</div>
			</div>

			{selectedCount > 0 && (
				<div className="fixed bottom-8 left-1/2 transform -translate-x-1/2">
					<Link href={`/project/${params.id}`} className="btn btn-primary px-6" type="button">
						Select Subreddits to Continue
					</Link>
				</div>
			)}
		</div>
	)
}
