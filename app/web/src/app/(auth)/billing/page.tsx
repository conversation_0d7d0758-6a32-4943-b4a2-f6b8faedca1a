import { verifyAuthToken } from '@/utils/firebase-admin'
import { redirect } from 'next/navigation'
import { db, schema } from '@packages/shared'
import { desc, eq } from 'drizzle-orm'
import { createBillingPortalSession } from '@/services/stripe'

export default async function Billing() {
	const user = await verifyAuthToken()
	if (!user) {
		return redirect('/login')
	}
	const subscription = await db.query.subscription.findFirst({
		where: eq(schema.subscription.userId, user.uid),
		orderBy: desc(schema.subscription.createdAt),
	})
	if (!subscription || !subscription.stripeCustomerId) {
		return redirect('/plans')
	}
	const session = await createBillingPortalSession(subscription.stripeCustomerId)
	if (!session.success || !session.url) {
		return redirect('/plans')
	}
	redirect(session.url)
}
