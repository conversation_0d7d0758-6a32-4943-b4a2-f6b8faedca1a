describe('Route Protection and Redirection', () => {
	beforeEach(() => {
		// Clear cookies and localStorage before each test
		cy.clearCookies()
		cy.clearLocalStorage()
	})

	it('should redirect from protected route to login when not authenticated', () => {
		// Directly visit the protected route
		cy.visit('/project/new', {
			failOnStatusCode: false,
			timeout: 30000,
		})

		// Check that we're redirected to login
		cy.location('pathname', { timeout: 30000 }).should('equal', '/login')
	})

	it('should stay on login page when not authenticated', () => {
		cy.visit('/login')
		cy.location('pathname').should('equal', '/login')
	})

	it('should stay on signup page when not authenticated', () => {
		cy.visit('/signup')
		cy.location('pathname').should('equal', '/signup')
	})
})
