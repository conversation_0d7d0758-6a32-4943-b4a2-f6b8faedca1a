import { Card, CardContent } from '@/components/ui/card'
import { MessageSquare } from 'lucide-react'

interface TestimonialProps {
	quote: string
	name: string
	title: string
	date?: string
	stars?: number
	image?: string
}

const StarRating = ({ rating }: { rating: number }) => {
	return (
		<div className="flex mb-2">
			{[...Array(5)].map((_, i) => (
				<svg
					key={i}
					className={`w-5 h-5 ${i < rating ? 'text-[#FF4500]' : 'text-gray-300'}`}
					fill="currentColor"
					viewBox="0 0 20 20"
				>
					<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
				</svg>
			))}
		</div>
	)
}

const TestimonialCard = ({ quote, name, title, date, stars, image }: TestimonialProps) => {
	return (
		<Card className="rounded-xl shadow-sm border h-full">
			<CardContent className="p-6">
				{stars && <StarRating rating={stars} />}
				<p className="text-gray-800 mb-4">{quote}</p>
				<div className="flex items-center mt-auto">
					<div className="w-10 h-10 rounded-full bg-gray-200 mr-3 overflow-hidden flex-shrink-0">
						{image ? <img src={image} alt={name} className="w-full h-full object-cover" /> : null}
					</div>
					<div>
						<h4 className="font-bold text-sm">{name}</h4>
						<p className="text-gray-600 text-xs">{title}</p>
						{date && <p className="text-gray-400 text-xs mt-1">{date}</p>}
					</div>
				</div>
			</CardContent>
		</Card>
	)
}

const Testimonials = () => {
	const testimonials = [
		{
			quote: 'LeadsAutomatic helped us go from invisible to trending in just 2 weeks. The AI-generated responses are spot-on and saved us countless hours of scrolling through subreddits.',
			name: 'Sophia Chen',
			title: 'Founder, CodeCraft',
			stars: 5,
			date: 'Mar 12, 2025',
			image: '/landing/assets/memoji.png'
		},
		{
			quote: 'We gained over 300 new users in our first month using LeadsAutomatic. The quality of engagement is what surprised me most - real conversations, not just shallow promotion.',
			name: 'Maria Kim',
			title: 'SaaS Entrepreneur',
			stars: 5,
			date: 'Feb 28, 2025',
			image: '/landing/assets/Ellipse-2.png'
		},
		{
			quote: "I must say LeadsAutomatic's quality, pricing and overall UX beats competitors by a mile. Great product team!",
			name: 'Julia Scott',
			title: '@h4ckergurl',
			stars: 5,
			date: 'Apr 11, 2025',
			image: '/landing/assets/memoji2.jpg'
		},
		{
			quote: 'I was skeptical about AI-generated responses, but LeadsAutomatic nails the Reddit tone perfectly. No more downvotes for sounding like an advertisement!',
			name: 'Amelia Torres',
			title: 'Indie Developer',
			stars: 5,
			date: 'Jan 15, 2025',
			image: '/landing/assets/Ellipse-4.png'
		},
		{
			quote: 'First time my content was published on r/SideProject, r/IndieHackers, and r/startups with top comments! This tool is truly amazing.',
			name: 'Robin de Wolf',
			title: '@robindewolf',
			stars: 5,
			date: 'Mar 23, 2025',
			image: '/landing/assets/Ellipse-1.png'
		},
		{
			quote: "Having my Reddit engagement optimized and handled daily is such a relief. It's doing the heavy lifting for me!",
			name: 'Luis',
			title: 'Startup Founder',
			stars: 5,
			date: 'Feb 19, 2025',
			image: '/landing/assets/animepfp.jpg'
		},
	]

	return (
		<section id="testimonials" className="py-20 bg-gray-50">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-16">
					<h2 className="text-3xl sm:text-5xl font-bold text-gray-900 mb-4">Loved by Busy Founders!</h2>
					<p className="text-xl text-gray-600 max-w-2xl mx-auto">
						Join the growing community of startup founders finding success on Reddit
					</p>
				</div>

				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
					{testimonials.map((testimonial, index) => (
						<TestimonialCard
							key={index}
							quote={testimonial.quote}
							name={testimonial.name}
							title={testimonial.title}
							stars={testimonial.stars}
							date={testimonial.date}
							image={testimonial.image}
						/>
					))}
				</div>
			</div>
		</section>
	)
}

export default Testimonials
