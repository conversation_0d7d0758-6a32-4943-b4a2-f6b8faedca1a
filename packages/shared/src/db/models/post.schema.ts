import {
	pgTable,
	uuid,
	varchar,
	timestamp,
	integer,
	boolean,
	text,
	jsonb,
	uniqueIndex,
	index,
	doublePrecision,
} from 'drizzle-orm/pg-core'
import { project } from './project.schema'

export const post = pgTable(
	'post',
	{
		id: uuid('id').defaultRandom().primaryKey(),
		projectId: uuid('project_id').references(() => project.id),

		redditId: varchar('reddit_id'), // 'id' from Reddit ('1k6jo8m')
		title: varchar('title').notNull(),
		author: varchar('author'),
		subreddit: varchar('subreddit'),
		permalink: varchar('permalink'),
		url: varchar('url'),
		selftext: text('selftext'),
		created: timestamp('created'),
		score: doublePrecision('score'),
		numComments: integer('num_comments'),
		thumbnail: varchar('thumbnail'),
		isOriginalContent: boolean('is_original_content'),
		isVideo: boolean('is_video'),
		upvoteRatio: doublePrecision('upvote_ratio'),
		sendReplies: boolean('send_replies'),

		// Optional: store the full response data
		rawData: jsonb('raw_data'),

		status: varchar('status', { enum: ['', 'processing', 'completed', 'failed', 'skipped'] }).default(''),
		chosen: boolean('chosen').default(false),
		chosenAt: timestamp('chosen_at'),
		chosenReason: text('chosen_reason'),
		relevanceScore: integer('relevance_score'),
		read: boolean('read').default(false),
	},
	(table) => [uniqueIndex('reddit_id_project_idx').on(table.redditId, table.projectId)],
)
