@import "tailwindcss";
@plugin "daisyui";
@plugin "daisyui/theme" {
	name: "automatic";
	default: true; /* set as default */
	prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
	color-scheme: light; /* color of browser-provided UI */

	--color-base-100: #ffffff;
	--color-base-200: #fef8f0;
	--color-base-300: #fef0e2;
	--color-base-content: #171717;
	--color-primary: #ea580c;
	--color-primary-content: #ffffff;
	--color-secondary: #fef0e2;
	--color-secondary-content: #171717;
	--color-accent: #f57c00;
	--color-accent-content: #ffffff;
	--color-neutral: #f5f5f5;
	--color-neutral-content: #171717;
	--color-info: #3abff8;
	--color-info-content: #ffffff;
	--color-success: #36d399;
	--color-success-content: #ffffff;
	--color-warning: #fbbd23;
	--color-warning-content: #171717;
	--color-error: #f87272;
	--color-error-content: #ffffff;

	/* border radius */
	--radius-selector: 1rem;
	--radius-field: 0.25rem;
	--radius-box: 0.5rem;

	/* base sizes */
	--size-selector: 0.25rem;
	--size-field: 0.25rem;

	/* border size */
	--border: 1px;

	/* effects */
	--depth: 1;
	--noise: 0;
}

@layer base {
	:root {
		--background: #ffffff;
		--foreground: #171717;
	}
}
@layer components {
	.container {
		@apply max-w-7xl mx-auto px-4 sm:px-6;
	}
}

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
	:root {
		--background: #0a0a0a;
		--foreground: #ededed;
	}
}

body {
	background: var(--background);
	color: var(--foreground);
	font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
	.btn {
		@apply rounded-full;
	}
}
