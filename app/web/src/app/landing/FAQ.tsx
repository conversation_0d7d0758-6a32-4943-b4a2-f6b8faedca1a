import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'

const FAQ = () => {
	const faqs = [
		{
			question: 'How does the Reddit monitoring work?',
			answer: 'LeadsAutomatic continuously scans relevant subreddits where your target audience hangs out. Our AI identifies posts where your product or service could provide value. We analyze factors like post relevance, engagement potential, and alignment with your offering to find the best opportunities.',
		},
		{
			question: 'Will the reply suggestions sound authentic?',
			answer: 'Absolutely! We train our AI to understand the unique culture and communication style of Reddit. Our suggestions are crafted to be helpful, authentic, and valuable to the conversation - never pushy or promotional. We avoid the marketing-speak that Reddit users typically downvote.',
		},
		{
			question: 'Can I manage multiple products with your service?',
			answer: "Yes! You can set up different product profiles within your LeadsAutomatic account. Each profile can have its own set of keywords, target subreddits, and company information so you'll get relevant suggestions for each of your products.",
		},
		{
			question: 'What kind of results can I expect?',
			answer: 'While results vary based on your product and industry, our users typically see increased website traffic, higher sign-up rates, and improved brand awareness within the first month. The key is consistency - regularly engaging with relevant Reddit conversations builds credibility over time.',
		},
		{
			question: 'Do you support international markets?',
			answer: 'Yes! LeadsAutomatic can monitor subreddits in any language and generate responses in the same language. We currently support English, Spanish, French, German, and Japanese, with more languages coming soon.',
		},
	]

	return (
		<section id="faq" className="py-20">
			<div className="max-w-3xl mx-auto px-4">
				<div className="text-center mb-12">
					<h2 className="text-3xl sm:text-5xl font-bold text-gray-900 mb-4">Have Questions?</h2>
					<p className="text-xl text-gray-600">
						If you can't find what you're looking for, feel free to reach out!
					</p>
				</div>

				<Accordion type="single" collapsible className="w-full">
					{faqs.map((faq, index) => (
						<AccordionItem key={index} value={`item-${index}`}>
							<AccordionTrigger className="text-left font-medium text-gray-900">
								{faq.question}
							</AccordionTrigger>
							<AccordionContent className="text-gray-600">{faq.answer}</AccordionContent>
						</AccordionItem>
					))}
				</Accordion>
			</div>
		</section>
	)
}

export default FAQ
