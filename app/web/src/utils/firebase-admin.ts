import { initializeApp, cert, getApps } from 'firebase-admin/app'
import { getAuth } from 'firebase-admin/auth'
import { cookies } from 'next/headers'

import { connection } from 'next/server'

// Initialize Firebase Admin
function initAdmin() {
	const apps = getApps()

	if (!apps.length) {
		try {
			const serviceAccount = {
				type: process.env.FIREBASE_TYPE,
				project_id: process.env.FIREBASE_PROJECT_ID,
				private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
				private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'), // Convert escaped newlines
				client_email: process.env.FIREBASE_CLIENT_EMAIL,
				client_id: process.env.FIREBASE_CLIENT_ID,
				auth_uri: process.env.FIREBASE_AUTH_URI,
				token_uri: process.env.FIREBASE_TOKEN_URI,
				auth_provider_x509_cert_url: process.env.FIREBASE_AUTH_PROVIDER_CERT_URL,
				client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL,
				universe_domain: process.env.FIREBASE_UNIVERSE_DOMAIN,
			}

			initializeApp({
				credential: cert(serviceAccount as any),
			})
		} catch (error) {
			console.error('Firebase Admin initialization error:', error)
		}
	}

	return getAuth()
}

// Get the Firebase Admin auth instance lazily
let auth: ReturnType<typeof getAuth> | null = null

/**
 * Verify a Firebase ID token from cookies
 * @returns The decoded token with the user's UID or null if invalid
 */
export async function verifyAuthToken() {
	try {
		// Initialize auth if not already done
		if (!auth) {
			await connection()
			auth = initAdmin()
		}

		const cookieStore = await cookies()
		const token = cookieStore.get('token')?.value
		if (!token) {
			return null
		}

		// Verify the token
		const decodedToken = await auth.verifyIdToken(token)
		return {
			uid: decodedToken.uid,
			email: decodedToken.email,
		}
	} catch (error) {
		console.error('Error verifying auth token:', error)
		return null
	}
}

export function getFirebaseAuth() {
	if (!auth) {
		auth = initAdmin()
	}
	return auth
}
