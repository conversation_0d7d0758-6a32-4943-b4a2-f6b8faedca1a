export const dynamic = 'force-dynamic'
import { env } from 'next-runtime-env'
import { headers } from 'next/headers'
import { connection } from 'next/server'
import Stripe from 'stripe'

// Initialize Stripe with the API key

/**
 * Create a Stripe customer
 * @param email Customer email
 * @param name Customer name (optional)
 * @returns Stripe customer object
 */
export async function createCustomer(email: string, name?: string) {
	const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '')
	try {
		const customer = await stripe.customers.create({
			email,
			name,
		})
		return { success: true, customer }
	} catch (error: any) {
		console.error('Error creating Stripe customer:', error)
		return { success: false, error: error.message }
	}
}

/**
 * Create a checkout session for a subscription with trial
 * @param customerId Stripe customer ID
 * @param priceId Stripe price ID
 * @param trialDays Number of trial days
 * @param metadata Additional metadata
 * @returns Checkout session URL
 */
/**
 * Create a checkout session for a subscription with trial
 * @param customerId Stripe customer ID
 * @param priceId Stripe price ID
 * @param trialDays Number of trial days (must be at least 1)
 * @param metadata Additional metadata
 * @returns Checkout session URL
 */
export async function createCheckoutSession(
	customerId: string,
	priceId: string,
	trialDays: number = 3,
	metadata: Record<string, string> = {},
) {
	const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '')
	try {
		const session = await stripe.checkout.sessions.create({
			customer: customerId,
			payment_method_types: ['card'],
			line_items: [
				{
					price: priceId,
					quantity: 1,
				},
			],
			mode: 'subscription',
			subscription_data: {
				trial_period_days: trialDays,
				metadata,
			},
			allow_promotion_codes: true,
			success_url: `${env('NEXT_PUBLIC_APP_URL')}/project/new?session_id={CHECKOUT_SESSION_ID}`,
			cancel_url: `${env('NEXT_PUBLIC_APP_URL')}/plans`,
			metadata,
		})

		return { success: true, url: session.url }
	} catch (error: any) {
		console.error('Error creating Stripe checkout session:', error)
		return { success: false, error: error.message }
	}
}

/**
 * Create a checkout session for a subscription without a trial
 * @param customerId Stripe customer ID
 * @param priceId Stripe price ID
 * @param metadata Additional metadata
 * @returns Checkout session URL
 */
export async function createCheckoutSessionWithoutTrial(
	customerId: string,
	priceId: string,
	metadata: Record<string, string> = {},
) {
	const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '')
	try {
		const session = await stripe.checkout.sessions.create({
			customer: customerId,
			payment_method_types: ['card'],
			line_items: [
				{
					price: priceId,
					quantity: 1,
				},
			],
			mode: 'subscription',
			subscription_data: {
				metadata,
			},
			allow_promotion_codes: true,
			success_url: `${env('NEXT_PUBLIC_APP_URL')}/project/new?session_id={CHECKOUT_SESSION_ID}`,
			cancel_url: `${env('NEXT_PUBLIC_APP_URL')}/plans`,
			metadata,
		})

		return { success: true, url: session.url }
	} catch (error: any) {
		console.error('Error creating Stripe checkout session without trial:', error)
		return { success: false, error: error.message }
	}
}

/**
 * Retrieve a subscription
 * @param subscriptionId Stripe subscription ID
 * @returns Subscription details
 */
export async function getSubscription(subscriptionId: string) {
	const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '')
	try {
		const subscription = await stripe.subscriptions.retrieve(subscriptionId)
		return { success: true, subscription }
	} catch (error: any) {
		console.error('Error retrieving Stripe subscription:', error)
		return { success: false, error: error.message }
	}
}

/**
 * Cancel a subscription
 * @param subscriptionId Stripe subscription ID
 * @returns Cancellation status
 */
export async function cancelSubscription(subscriptionId: string) {
	const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '')
	try {
		const subscription = await stripe.subscriptions.cancel(subscriptionId)
		return { success: true, subscription }
	} catch (error: any) {
		console.error('Error canceling Stripe subscription:', error)
		return { success: false, error: error.message }
	}
}

/**
 * Verify a Stripe webhook signature
 * @param signature Signature from the request header
 * @param payload Raw request body
 * @returns Whether the signature is valid
 */
export async function verifyWebhookSignature(signature: string, payload: string) {
	const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '')
	await connection()
	try {
		const event = stripe.webhooks.constructEvent(payload, signature, process.env.STRIPE_WEBHOOK_SECRET || '')
		return { success: true, event }
	} catch (error: any) {
		console.error('Error verifying Stripe webhook signature:')
		return { success: false, error: error.message }
	}
}

export async function createBillingPortalSession(customerId: string) {
	const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '')
	const headersList = await headers()
	const referer = headersList.get('referer')
	try {
		// const configuration = await stripe.billingPortal.configurations.create({
		//   features: {
		//     invoice_history: {
		//       enabled: true,
		//     },
		//     payment_method_update: {
		//       enabled: true,
		//     },
		//     subscription_cancel: {
		//       enabled: true,
		//     },
		//   },
		// });
		// console.log(await stripe.billingPortal.configurations.list())
		const session = await stripe.billingPortal.sessions.create({
			customer: customerId,
			configuration: 'bpc_1RIo5MPIrdCxsqBkN7fKBnsp',
			return_url: referer || `${env('NEXT_PUBLIC_APP_URL')}/project`,
		})
		return { success: true, url: session.url }
	} catch (error: any) {
		console.error('Error creating Stripe billing portal session:', error)
		return { success: false, error: error.message }
	}
}
