'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useUserStore } from '@/store/userStore'
import { useRouter } from 'next/navigation'
import { getUserProjects } from '@/actions/project'

export default function ProjectsPage() {
	const { isLoggedIn } = useUserStore() as { isLoggedIn: boolean }
	const router = useRouter()
	const [searchQuery, setSearchQuery] = useState('')
	const [projects, setProjects] = useState<any[]>([])
	const [loading, setLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)

	// Fetch projects when component mounts
	useEffect(() => {
		// Check if user is logged in
		// if (!isLoggedIn) {
		//   router.push('/login')
		//   return
		// }

		// Fetch projects
		const fetchProjects = async () => {
			try {
				setLoading(true)
				setError(null)

				const result = await getUserProjects()

				if (result.success && result.projects) {
					setProjects(result.projects)
				} else {
					setError((result.error as string) || 'Failed to load projects')
				}
			} catch (err) {
				console.error('Error fetching projects:', err)
				setError('An unexpected error occurred')
			} finally {
				setLoading(false)
			}
		}

		fetchProjects()
	}, [isLoggedIn, router])

	// Handle refresh button click
	const handleRefresh = () => {
		setLoading(true)
		getUserProjects()
			.then((result) => {
				if (result.success && result.projects) {
					setProjects(result.projects)
				} else {
					setError((result.error as string) || 'Failed to refresh projects')
				}
				setLoading(false)
			})
			.catch((err) => {
				console.error('Error refreshing projects:', err)
				setError('An unexpected error occurred')
				setLoading(false)
			})
	}

	// Helper function to format time ago
	function getTimeAgo(date: Date): string {
		const now = new Date()
		const diffInMilliseconds = now.getTime() - date.getTime()
		const diffInMinutes = Math.floor(diffInMilliseconds / (1000 * 60))

		if (diffInMinutes < 60) return `${diffInMinutes} minutes`
		if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours`
		return `${Math.floor(diffInMinutes / 1440)} days`
	}

	return (
		<div className="container py-8">
			<div className="bg-secondary bg-opacity-30 p-6 rounded-lg mb-8">
				<div className="flex justify-between items-center">
					<div>
						<h1 className="text-3xl font-bold text-base-content mb-2">Products</h1>
						<p className="text-gray-600">
							Manage your lead generation products and discover relevant leads.
						</p>
					</div>
					<button
						type="button"
						className="btn btn-primary text-white"
						onClick={() => router.push('/project/new')}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							className="h-5 w-5 mr-1"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<title>Add</title>
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
						</svg>
						Create Product
					</button>
				</div>
			</div>

			<div className="flex justify-between items-center mb-6">
				<div className="relative w-full max-w-md">
					<input
						type="text"
						placeholder="Search products..."
						className="input input-bordered w-full pr-10"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
					/>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="h-5 w-5 absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<title>Search</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
						/>
					</svg>
				</div>
				<button type="button" className="btn btn-outline gap-2" onClick={handleRefresh} disabled={loading}>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="h-5 w-5"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<title>Refresh</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
						/>
					</svg>
					{loading ? 'Loading...' : 'Refresh Data'}
				</button>
			</div>

			{/* Loading state */}
			{loading && (
				<div className="flex justify-center items-center py-12">
					<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
				</div>
			)}

			{/* Error state */}
			{error && (
				<div className="bg-error bg-opacity-10 text-error p-4 rounded-lg mb-6">
					<p className="font-medium">{error}</p>
					<button className="btn btn-sm btn-error mt-2" onClick={handleRefresh}>
						Try Again
					</button>
				</div>
			)}

			{/* No projects state */}
			{!loading && !error && projects.length === 0 && (
				<div className="text-center py-12">
					<h3 className="text-xl font-medium mb-2">No projects found</h3>
					<p className="text-gray-600 mb-4">Create your first project to get started</p>
					<button
						type="button"
						className="btn btn-primary text-white"
						onClick={() => router.push('/project/new')}
					>
						Create Project
					</button>
				</div>
			)}

			{/* Projects grid */}
			{!loading && !error && projects.length > 0 && (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
					{projects.map((project) => (
						<div
							key={project.id}
							className="relative card overflow-hidden rounded-lg border shadow-lg shadow-base-200 border-base-200"
						>
							{/* Header section with beige background */}
							<div className="bg-secondary bg-opacity-20 p-4">
								<div className="flex justify-between items-start">
									<h2 className="text-xl font-bold">
										<Link
											href={`/project/${project.id}`}
											className="before:content-[''] before:absolute before:inset-0 before:hover:opacity-100"
										>
											{project.name}
										</Link>
									</h2>
									<Link
										href={`/project/${project.id}`}
										className="btn btn-ghost btn-sm btn-circle relative"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											className="h-5 w-5"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Edit</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
											/>
										</svg>
									</Link>
								</div>
								<p className="text-sm text-gray-500 flex items-center">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-4 w-4 mr-1"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<title>Time</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
									Created {getTimeAgo(new Date(project.createdAt))} ago
								</p>
							</div>

							{/* Content section */}
							<div className="p-4 bg-base-100">
								<p className="text-gray-600 mb-3 line-clamp-4 overflow-hidden">{project.description}</p>

								<div className="flex flex-wrap gap-2 mb-4">
									{project.keywords &&
										project.keywords
											.split(',')
											.slice(0, 5)
											.map((keyword: string, i: number) => (
												<span key={`${project.id}-tag-${i}`} className="badge badge-outline">
													{keyword.trim()}
												</span>
											))}
									{project.keywords && project.keywords.split(',').length > 5 && (
										<span className="badge badge-outline badge-primary">
											+{project.keywords.split(',').length - 5} more
										</span>
									)}
								</div>
							</div>

							{/* Actions section */}
							<div className="p-4 bg-base-100 pt-0 flex gap-2 mt-auto">
								<Link
									href={`/project/${project.id}`}
									className="relative btn btn-primary re text-white flex-1"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 mr-1"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<title>See Leads</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
										/>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
										/>
									</svg>
									See Leads
								</Link>
								<button type="button" className="relative btn btn-outline flex-1" disabled>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-5 w-5 mr-1"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<title>Strategy</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
										/>
									</svg>
									Strategy
								</button>
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	)
}
