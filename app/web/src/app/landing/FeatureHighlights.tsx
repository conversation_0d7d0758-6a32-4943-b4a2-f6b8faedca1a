import { Button } from '@/components/ui/button'
import { ArrowRight } from 'lucide-react'
import Link from 'next/link'
const FeatureSection = ({
	title,
	description,
	children,
	buttonText,
	imageSide = 'right',
	bgColor = 'bg-gray-50',
}: {
	title: string
	description: string
	children: React.ReactNode
	buttonText?: string
	imageSide?: 'left' | 'right'
	bgColor?: string
}) => {
	return (
		<div className={`py-16 ${bgColor} rounded-3xl overflow-hidden`}>
			<div className="max-w-6xl mx-auto px-4">
				<div
					className={`grid grid-cols-1 md:grid-cols-2 gap-12 items-center ${imageSide === 'left' ? 'md:flex-row-reverse' : ''}`}
				>
					<div className={`${imageSide === 'left' ? 'md:order-2' : ''}`}>
						<h3 className="text-3xl md:text-4xl font-bold mb-4">{title}</h3>
						<p className="text-gray-600 mb-6">{description}</p>
						{buttonText && (
							<Link href="/signup">
								<Button className="bg-[#FF4500] hover:bg-[#e03d00] text-white">
									{buttonText} <ArrowRight className="ml-2 h-4 w-4" />
								</Button>
							</Link>
						)}
					</div>

					<div className={`${imageSide === 'left' ? 'md:order-1' : ''}`}>{children}</div>
				</div>
			</div>
		</div>
	)
}

const FeatureHighlights = () => {
	return (
		<section className="py-16">
			<div className="max-w-6xl mx-auto px-4">
				{/* Feature Intro */}
				<div className="text-center mb-16">
					<h2 className="text-4xl sm:text-5xl font-bold mb-4">
						Unlock your <span className="text-[#FF4500]">Reddit growth</span>
					</h2>
					<p className="text-xl text-gray-600 max-w-3xl mx-auto">
						Get new personalized Reddit opportunities daily, effortlessly.
					</p>
				</div>

				<div className="space-y-16">
					{/* Feature 1: Automate Reddit analysis */}
					<FeatureSection
						title="Automate Reddit analysis and keyword research"
						description="Analyze and find the best engagement opportunities in your niche. Discover trending posts that match your product. Generate personalized reply templates anytime."
						buttonText="Start now"
						bgColor="bg-gray-50"
					>
						<div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
							<div className="relative">
								<div className="absolute top-0 right-10 -translate-y-1/2">
									<div className="bg-[#fff1eb] px-3 py-1 rounded-full text-[#FF4500] text-sm font-medium">
										<span className="flex items-center">Automatically created</span>
									</div>
								</div>

								<div className="mt-8 grid grid-cols-3 gap-2">
									<div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
										<div className="text-xs font-medium text-gray-700 mb-1">Apr 20</div>
										<div className="text-xs text-gray-500">r/programming</div>
									</div>
									<div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
										<div className="text-xs font-medium text-gray-700 mb-1">Apr 21</div>
										<div className="text-xs text-gray-500">r/webdev</div>
									</div>
									<div className="bg-[#fff1eb] p-3 rounded-lg border border-[#FF4500]/20">
										<div className="text-xs font-medium text-[#FF4500] mb-1">Apr 22</div>
										<div className="text-xs text-gray-500">r/SideProject</div>
									</div>
									<div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
										<div className="text-xs font-medium text-gray-700 mb-1">Apr 23</div>
										<div className="text-xs text-gray-500">r/startups</div>
									</div>
									<div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
										<div className="text-xs font-medium text-gray-700 mb-1">Apr 24</div>
										<div className="text-xs text-gray-500">r/SaaS</div>
									</div>
									<div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
										<div className="text-xs font-medium text-gray-700 mb-1">Apr 25</div>
										<div className="text-xs text-gray-500">r/IndieDev</div>
									</div>
								</div>
							</div>
						</div>
					</FeatureSection>

					{/* Feature 2: Write replies that sound like you */}
					<FeatureSection
						title="Find real buyers"
						description="Leads Automatic will find you the best opportunities to get in front of real buyers. No more wasting time on fake leads."
						buttonText="Start for Free"
						imageSide="left"
						bgColor="bg-[#fff8f5]"
					>
						<div className="relative">


							<div className="mt-8 bg-white rounded-xl shadow-lg p-6 border border-gray-100">
								<div className="space-y-3">
									<div className="flex items-center mb-4">
										<div className="p-2 bg-gray-100 rounded-md mr-3">
											<svg
												width="20"
												height="20"
												viewBox="0 0 24 24"
												fill="none"
												xmlns="http://www.w3.org/2000/svg"
											>
												<path
													d="M9 12H15"
													stroke="#666"
													strokeWidth="2"
													strokeLinecap="round"
													strokeLinejoin="round"
												/>
												<path
													d="M12 9V15"
													stroke="#666"
													strokeWidth="2"
													strokeLinecap="round"
													strokeLinejoin="round"
												/>
												<path
													d="M3 12C3 13.1819 3.23279 14.3522 3.68508 15.4442C4.13738 16.5361 4.80031 17.5282 5.63604 18.364C6.47177 19.1997 7.46392 19.8626 8.55585 20.3149C9.64778 20.7672 10.8181 21 12 21C13.1819 21 14.3522 20.7672 15.4442 20.3149C16.5361 19.8626 17.5282 19.1997 18.364 18.364C19.1997 17.5282 19.8626 16.5361 20.3149 15.4442C20.7672 14.3522 21 13.1819 21 12C21 9.61305 20.0518 7.32387 18.364 5.63604C16.6761 3.94821 14.3869 3 12 3C9.61305 3 7.32387 3.94821 5.63604 5.63604C3.94821 7.32387 3 9.61305 3 12Z"
													stroke="#666"
													strokeWidth="2"
													strokeLinecap="round"
													strokeLinejoin="round"
												/>
											</svg>
										</div>
										<div className="text-sm font-medium">Like a Redditor</div>
									</div>

									<div className="space-y-2">
										<div className="bg-gray-50 rounded-lg p-4 space-y-3">
											<div className="flex items-center space-x-2">
												<div className="w-6 h-6 rounded-full bg-[#FF4500] flex items-center justify-center text-white text-xs font-bold">r/</div>
												<div className="text-sm font-medium">r/wallstreetbets</div>
											</div>
											<div className="text-sm text-gray-600">
												"Just YOLO'd my life savings into $ROPE calls. What's the best way to track my portfolio?"
											</div>
											<div className="bg-white rounded p-3 text-sm text-gray-700 border border-gray-100">
												<div className="flex items-center mb-2">
													<div className="w-4 h-4 rounded-full bg-[#FF4500] mr-2"></div>
													<span className="text-xs text-gray-500">StonkTracker</span>
												</div>
												<p>💎🙌 Ape here. First off, respect for the $ROPE play - that's some galaxy brain thinking right there. For tracking, I've been using this tool called StonkTracker (not financial advice, I'm literally eating crayons rn).</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</FeatureSection>

					{/* Feature 3: Generate quality replies */}
					<FeatureSection
						title="Generate personalized reply suggestions"
						description="Create authentic, high-quality Reddit replies that feel genuine, not promotional. Our AI adapts to subreddit culture and ensures your contributions add value to the conversation."
						buttonText="Start for Free"
						bgColor="bg-gray-50"
					>
						<div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
							<div className="space-y-4">
								<div className="bg-gray-50 p-4 rounded-lg border border-gray-100 relative">
									<div className="absolute top-0 right-8 -translate-y-1/2">
										<div className="bg-[#fff1eb] px-3 py-1 rounded-full text-[#FF4500] text-sm font-medium">
											Helpful replies
										</div>
									</div>

									<div className="mb-2">
										<span className="text-sm font-medium">Original post on r/SideProject:</span>
									</div>
									<p className="text-sm text-gray-600">
										"I built a tool that helps video creators optimize their YouTube content. Would
										love feedback on my landing page!"
									</p>
								</div>

								<div className="bg-[#fff8f5] p-4 rounded-lg border border-[#FF4500]/10">
									<div className="mb-2 flex items-center justify-between">
										<span className="text-sm font-medium">LeadsAutomatic Suggestion:</span>
										<span className="text-xs bg-[#FF4500] text-white px-2 py-0.5 rounded">
											98% match
										</span>
									</div>
									<p className="text-sm text-gray-600">
										"Just checked out your landing page - nice work! As a fellow indie maker, I
										especially like how you clearly explain the problem you're solving. One
										suggestion: maybe add a short video demo showing the actual workflow? That
										really helped conversions for my Reddit tool. Also, have you considered
										targeting specific niches like gaming or tech reviewers? Happy to chat more if
										helpful!"
									</p>
								</div>
							</div>
						</div>
					</FeatureSection>
				</div>
			</div>
		</section>
	)
}

export default FeatureHighlights
