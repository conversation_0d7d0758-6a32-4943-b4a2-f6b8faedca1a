# syntax=docker/dockerfile:1.4
FROM node:22-alpine AS base
WORKDIR /app
COPY package.json ./
COPY yarn.lock ./

# Development stage
FROM base AS development
ENV NODE_ENV=development
RUN yarn install
CMD ["yarn", "dev"]

FROM peweo/automatic-shared:prod AS shared-build
WORKDIR /app

# Build stage for production
FROM base AS build
RUN yarn install
COPY --from=shared-build /app/ ./node_modules/@packages/shared/
COPY . .
RUN yarn build

# Production stage
FROM node:22-alpine AS production
WORKDIR /app
COPY --from=build /app/package*.json ./
COPY --from=build /app/dist ./dist
COPY --from=build /app/node_modules ./node_modules
ENV NODE_ENV=production
EXPOSE 3001
CMD ["yarn", "start"]
