import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Check, ArrowRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
const PricingFeature = ({ text }: { text: string }) => {
	return (
		<li className="flex items-start py-2">
			<div className="flex-shrink-0 mr-3">
				<Check className="h-5 w-5 text-[#FF4500]" />
			</div>
			<span className="text-gray-700">{text}</span>
		</li>
	)
}

const Pricing = () => {
	const features = [
		'Unlimited Reply Suggestions per month',
		'Monitoring across 10 subreddits',
		'AI-powered reply drafting',
		'Daily email delivery (Coming soon)',
		'Advanced keyword tracking',
		'Reply performance analytics',
		'Unlimited edits to suggestions',
		'Priority support',
	]

	return (
		<section id="pricing" className="py-20">
			<div className="max-w-6xl mx-auto px-4">
				<div className="flex flex-col lg:flex-row lg:items-center mb-16 gap-10">
					<div className="lg:w-1/2">
						<h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
							Grow Organic <br />
							Presence on <span className="text-[#FF4500]">Auto-Pilot</span>
						</h2>
						<p className="text-xl text-gray-600 mb-4">
							LeadsAutomatic monitors Reddit for opportunities while you focus on growing your business.
						</p>
						<div className="flex items-center mt-8">
							<div className="flex -space-x-2">
								<div className="relative w-10 h-10">
									<Image
										src="/landing/assets/Ellipse-1.png"
										alt="Ellipse 1"
										width={40}
										height={40}
										priority
										className="object-contain"
									/>
								</div>
								<div className="relative w-10 h-10">
									<Image
										src="/landing/assets/Ellipse-2.png"
										alt="Ellipse 2"
										width={40}
										height={40}
										priority
										className="object-contain"
									/>
								</div>
								<div className="relative w-10 h-10">
									<Image
										src="/landing/assets/Ellipse-3.png"
										alt="Ellipse 3"
										width={40}
										height={40}
										priority
										className="object-contain"
									/>
								</div>
								<div className="relative w-10 h-10">
									<Image
										src="/landing/assets/Ellipse-4.png"
										alt="Ellipse 4"
										width={40}
										height={40}
										priority
										className="object-contain"
									/>
								</div>
							</div>
							<div className="ml-4">
								<span className="block font-bold text-gray-900">740+ Startups</span>
								<span className="text-sm text-gray-500">Finding Success on Reddit</span>
							</div>
						</div>
					</div>

					<div className="lg:w-1/2 mt-10 lg:mt-0">
						<Card className="rounded-2xl shadow-xl border border-[#FF4500]/20 overflow-hidden">
							<CardContent className="p-0">
								<div className="p-8">
									<div className="flex justify-between items-start mb-6">
										<div>
											<span className="inline-block px-3 py-1 bg-[#fff1eb] text-[#FF4500] rounded-full text-sm font-medium mb-2">
												EARLY ACCESS
											</span>
											<h3 className="text-2xl font-bold">Founder Plan</h3>
											<p className="text-gray-600 text-sm mt-1">For ambitious founders</p>
										</div>
										<div className="text-right">
											<div className="flex items-end">
												<span className="text-4xl font-bold">$29</span>
												<span className="text-gray-500 ml-1 mb-1">/month</span>
											</div>
											<span className="text-gray-400 line-through text-sm">$49</span>
										</div>
									</div>

									<div className="border-t border-gray-100 pt-6 pb-2">
										<h4 className="font-medium mb-2">What's included:</h4>
										<ul className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
											{features.map((feature, index) => (
												<PricingFeature key={index} text={feature} />
											))}
										</ul>
									</div>
								</div>

								<div className="px-8 pb-8">
									<Link href="/signup">
										<Button className="w-full py-6 bg-[#FF4500] hover:bg-[#e03d00] text-lg text-white">
											Get Started Now <ArrowRight className="ml-2 h-5 w-5" />
										</Button>
									</Link>
									<p className="text-center text-gray-500 text-sm mt-3">
										Cancel anytime. No questions asked!
									</p>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</section>
	)
}

export default Pricing
