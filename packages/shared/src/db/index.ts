import { drizzle } from 'drizzle-orm/node-postgres'
import { Pool } from 'pg'
import dbConfig from '../drizzle.config'
import * as schema from './models/index.schema'

const pool = new Pool({
	// @ts-ignore
	connectionString: dbConfig?.dbCredentials?.url,
	max: 20,
	idleTimeoutMillis: 30000,
	connectionTimeoutMillis: 2000,
})

const db = drizzle({ schema, client: pool })
export default db
export { schema }
