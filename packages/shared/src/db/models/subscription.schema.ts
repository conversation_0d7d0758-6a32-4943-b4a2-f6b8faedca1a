import { pgTable, uuid, varchar, timestamp, boolean } from 'drizzle-orm/pg-core'
import { user } from './user.schema'

export const subscription = pgTable('subscription', {
	// Primary identifier
	id: uuid('id').defaultRandom().primaryKey(),

	// User reference
	userId: varchar('user_id', { length: 128 })
		.notNull()
		.references(() => user.id),

	// Stripe data
	stripeCustomerId: varchar('stripe_customer_id', { length: 255 }),
	stripeSubscriptionId: varchar('stripe_subscription_id', { length: 255 }),
	stripePaymentMethodId: varchar('stripe_payment_method_id', { length: 255 }),
	stripePriceId: varchar('stripe_price_id', { length: 255 }),

	// Subscription status
	status: varchar('status', { length: 50 }).notNull().default('trial'), // trial, active, past_due, canceled, expired

	// Trial information
	isTrialActive: boolean('is_trial_active').default(true).notNull(),
	trialStartDate: timestamp('trial_start_date').defaultNow().notNull(),
	trialEndDate: timestamp('trial_end_date'),

	// Timestamps
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull(),
	canceledAt: timestamp('canceled_at'),
})
