services:
  shared:
    image: peweo/automatic-shared:prod
    build:
      context: ./packages/shared
      target: production
      dockerfile: Dockerfile
    env_file:
      - .env
      - ./env/.env.shared
    networks:
      - automatic-net
    depends_on:
      db:
        condition: service_healthy
  web:
    image: peweo/automatic-fe:prod
    build:
      context: ./app/web
      target: production
      dockerfile: Dockerfile
    stdin_open: true
    depends_on:
      shared:
        condition: service_completed_successfully
    # volumes:
      # - ./app/web:/app
      # - ./packages/shared:/app/node_modules/@packages/shared
      # - /app/node_modules
    restart: always
    ports:
      - 127.0.0.1:3000:3000
    networks:
      - automatic-net
    env_file:
      - .env
      - env/.env.web
      - env/.env.shared
  backend:
    image: peweo/automatic-be:prod
    restart: always
    build:
      context: ./app/backend
      target: production
      dockerfile: Dockerfile
    # volumes:
    #   - ./app/api:/app
    #   - ./packages/shared:/app/node_modules/@packages/shared
    #   - /app/node_modules
    networks:
      - automatic-net
    env_file:
      - ./env/.env.backend
      - ./env/.env.shared
      - .env
    depends_on:
      shared:
        condition: service_completed_successfully
      db:
        condition: service_healthy
      redis:
        condition: service_started
  db:
    image: pgvector/pgvector:pg17
    platform: linux/x86_64
    restart: always
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    volumes:
      - ./data/db:/var/lib/postgresql/data
    ports:
      - 127.0.0.1:5432:5432
    networks:
      - automatic-net
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_DATABASE} -h localhost || exit 1"]
      start_period: 10s
      interval: 60s
      timeout: 5s
      retries: 10
  redis:
    image: redis:latest
    networks:
      - automatic-net
networks:
  automatic-net:


# volumes:
#   shared:
#   mariadb_data_dev:
#   frontend-dist:
