CREATE TABLE "subscription" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar(128) NOT NULL,
	"status" varchar(50) DEFAULT 'trial' NOT NULL,
	"is_trial_active" boolean DEFAULT true NOT NULL,
	"trial_start_date" timestamp DEFAULT now() NOT NULL,
	"trial_end_date" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "plan" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" varchar(1000),
	"lemonsqueezy_variant_id" varchar(255) NOT NULL,
	"lemonsqueezy_product_id" varchar(255) NOT NULL,
	"price" integer NOT NULL,
	"interval" varchar(20) NOT NULL,
	"max_projects" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "plan_lemonsqueezy_variant_id_unique" UNIQUE("lemonsqueezy_variant_id")
);
--> statement-breakpoint
ALTER TABLE "subscription" ADD CONSTRAINT "subscription_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "lemonsqueezy_variant_id_idx" ON "plan" USING btree ("lemonsqueezy_variant_id");