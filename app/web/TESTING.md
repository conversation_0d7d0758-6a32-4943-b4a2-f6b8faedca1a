# Testing Authentication with <PERSON><PERSON>

This document explains how to run the Cypress tests for the authentication functionality.

## Prerequisites

- Make sure the application is running locally
- You need a test user account in Firebase for running the tests

## Running the Tests

### Open Cypress Test Runner

To open the Cypress Test Runner UI:

```bash
yarn cypress:open
```

This will open the Cypress Test Runner, where you can select and run individual tests.

### Run Tests in Headless Mode

To run all tests in headless mode (useful for CI/CD):

```bash
yarn cypress:run
```

### Run Tests with Application Start

To start the application and run the tests in one command:

```bash
yarn test:e2e
```

This command will:
1. Start the Next.js development server
2. Wait for the server to be available
3. Run the Cypress tests
4. Shut down the server when tests are complete

## Test Files

- `cypress/e2e/authentication.cy.ts`: Tests for email/password authentication
- `cypress/e2e/google-auth.cy.ts`: Tests for Google authentication (some tests are skipped)

## Test User Setup

For the tests to run successfully, you need to set up a test user in Firebase:

1. Create a test user in Firebase Authentication with the following credentials:
   - Email: <EMAIL>
   - Password: Test123!

2. Alternatively, you can modify the test files to use your own test credentials.

## Mocking Firebase Authentication

Some tests use mocking to avoid actual Firebase authentication calls. This is useful for testing the UI without making real authentication requests.

## Notes on Google Authentication Testing

Testing Google authentication is challenging because it involves third-party authentication flows. The tests for Google authentication are mostly UI tests and some are skipped because they would require special setup to handle the OAuth flow.

## Troubleshooting

If you encounter issues with the tests:

1. Make sure your Firebase configuration is correct
2. Check that the test user exists in Firebase
3. Verify that the application is running correctly
4. Check the Cypress logs for detailed error information
