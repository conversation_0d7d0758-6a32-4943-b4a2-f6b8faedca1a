import { getApp, getApps, initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { env } from 'next-runtime-env'

// Your web app's Firebase configuration
const firebaseConfig = {
	apiKey: env('NEXT_PUBLIC_FIREBASE_API_KEY'),
	authDomain: env('NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN'),
	projectId: env('NEXT_PUBLIC_FIREBASE_PROJECT_ID'),
	storageBucket: env('NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET'),
	messagingSenderId: env('NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID'),
	appId: env('NEXT_PUBLIC_FIREBASE_APP_ID'),
}

// Initialize Firebase only on the client side
const app =
	typeof window !== 'undefined'
		? getApps().length === 0
			? firebaseConfig.apiKey && initializeApp(firebaseConfig)
			: getApp()
		: ({} as any)
const auth = typeof window !== 'undefined' && app ? getAuth(app) : ({} as any)

// Set language only on client side
if (typeof window !== 'undefined' && auth) {
	auth?.useDeviceLanguage?.()
}

export { auth }
