'use client'

import { useUserStore } from '@/store/userStore'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createTrialCheckout, hasActiveSubscription, getUserSubscription } from '@/actions/subscription'
import Link from 'next/link'

export default function PlansPage() {
	const { user, isLoggedIn } = useUserStore() as { user: any; isLoggedIn: boolean }
	const router = useRouter()
	const [loading, setLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)
	const [checkingOut, setCheckingOut] = useState(false)
	const [hadTrial, setHadTrial] = useState(false)

	useEffect(() => {
		// Check if user is logged in
		if (!isLoggedIn && !loading) {
			router.push('/login')
			return
		}

		const fetchData = async () => {
			try {
				// Check if user already has a subscription
				if (user) {
					const subscriptionResult = await hasActiveSubscription(user.uid)

					if (subscriptionResult.success) {
						// If payment update is required (past_due status), redirect to billing page
						if (subscriptionResult.paymentUpdateRequired) {
							router.push('/billing')
							return
						}

						// If user has an active subscription, redirect to start project
						if (subscriptionResult.hasSubscription) {
							router.push('/project/new')
							return
						}
					}

					// Check if user has had a trial before
					const userSubscription = await getUserSubscription(user.uid)
					if (userSubscription.success && userSubscription.subscription) {
						// If user has a subscription record with expired trial, mark as had trial
						if (userSubscription.subscription.status === 'expired' ||
							(userSubscription.subscription.trialEndDate &&
								new Date(userSubscription.subscription.trialEndDate) < new Date())) {
							setHadTrial(true)
						}
					}
				}
			} catch (err) {
				console.error('Error fetching data:', err)
				setError('An unexpected error occurred')
			} finally {
				setLoading(false)
			}
		}

		if (isLoggedIn) {
			fetchData()
		} else {
			setLoading(false)
		}
	}, [isLoggedIn, router, user, loading])

	const handleSelectPlan = async () => {
		if (!user || !user.email) return

		setCheckingOut(true)
		setError(null)

		try {
			// Use the same checkout function for both trial and paid plan
			// If hadTrial is true, skip the trial period
			const result = await createTrialCheckout(user.uid, user.email, hadTrial)

			if (result.success && result.url) {
				// Redirect to checkout
				window.location.href = result.url
			} else {
				// If the user has already had a trial, update the UI to show that
				if (result.hadTrial) {
					setHadTrial(true)
					// Try again with skipTrial=true
					const retryResult = await createTrialCheckout(user.uid, user.email, true)
					if (retryResult.success && retryResult.url) {
						window.location.href = retryResult.url
						return
					}
				}
				setError(result.error || 'Failed to create checkout')
			}
		} catch (err) {
			console.error('Error creating checkout:', err)
			setError('An unexpected error occurred')
		} finally {
			setCheckingOut(false)
		}
	}

	if (loading) {
		return (
			<div className="flex justify-center items-center min-h-screen bg-[#fffaf7]">
				<div className="loading loading-spinner loading-lg text-primary" />
			</div>
		)
	}

	return (
		<div className="min-h-[calc(100vh-79px)] bg-[#fffaf7]">
			<div className="container mx-auto px-4 py-12">
				<div className="max-w-4xl mx-auto">
					<div className="text-center mb-12">
						<div className="font-bold text-2xl text-primary mb-2">leadsautomatic</div>
						<h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
						<p className="text-lg text-gray-600">
							{hadTrial
								? "Continue with our Pro plan to access all features"
								: "Get started with our Pro plan including a 3-day free trial"}
						</p>
					</div>

					{error && (
						<div className="max-w-md mx-auto mb-8">
							<div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg flex items-center">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									className="h-5 w-5 mr-2"
									viewBox="0 0 20 20"
									fill="currentColor"
								>
									<title>Error icon</title>
									<path
										fillRule="evenodd"
										d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
										clipRule="evenodd"
									/>
								</svg>
								<span>{error}</span>
							</div>
						</div>
					)}

					<div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
						{/* Pro Plan with Trial */}
						<div className="bg-white rounded-3xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
							<div className="p-8">
								<div className="flex items-center justify-between mb-6">
									<h2 className="text-2xl font-bold text-gray-900">Pro Plan</h2>
									<span className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
										{hadTrial ? 'Popular' : 'Recommended'}
									</span>
								</div>
								<div className="mb-6">
									<div className="flex items-baseline">
										<span className="text-4xl font-bold text-gray-900">$29</span>
										<span className="text-gray-500 ml-2">/month</span>
									</div>
									<p className="text-gray-500 mt-1">
										{hadTrial ? 'Billed monthly' : 'Includes 3-day free trial'}
									</p>
								</div>
								<ul className="space-y-4 mb-8">
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-primary mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">Full access to all features</span>
									</li>
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-primary mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">Priority support</span>
									</li>
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-primary mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">Advanced analytics</span>
									</li>
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-primary mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">Custom branding</span>
									</li>
								</ul>
								<button
									type="button"
									className="w-full cursor-pointer bg-primary text-white rounded-full py-3 px-6 font-medium hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
									disabled={checkingOut}
									onClick={handleSelectPlan}
								>
									{checkingOut ? (
										<div className="flex items-center justify-center">
											<div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
											Processing...
										</div>
									) : (
										hadTrial ? 'Purchase Plan' : 'Start 3-Day Free Trial'
									)}
								</button>
							</div>
						</div>

						{/* Enterprise Plan */}
						<div className="bg-white rounded-3xl shadow-lg overflow-hidden">
							<div className="p-8">
								<div className="flex items-center justify-between mb-6">
									<h2 className="text-2xl font-bold text-gray-900">Enterprise</h2>
									<span className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm font-medium">
										Custom
									</span>
								</div>
								<div className="mb-6">
									<div className="flex items-baseline">
										<span className="text-4xl font-bold text-gray-900">Custom</span>
									</div>
									<p className="text-gray-500 mt-1">Tailored to your needs</p>
								</div>
								<ul className="space-y-4 mb-8">
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-gray-400 mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">Everything in Pro, plus:</span>
									</li>
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-gray-400 mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">Dedicated account manager</span>
									</li>
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-gray-400 mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">Custom integrations</span>
									</li>
									<li className="flex items-start">
										<svg
											className="h-6 w-6 text-gray-400 mt-0.5 mr-3"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>Checkmark</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth="2"
												d="M5 13l4 4L19 7"
											/>
										</svg>
										<span className="text-gray-600">SLA guarantees</span>
									</li>
								</ul>
								<Link
									href="mailto:<EMAIL>"
									className="w-full cursor-pointer bg-gray-100 text-gray-600 rounded-full py-3 px-6 font-medium hover:bg-gray-200 transition-colors block text-center"
								>
									Contact Sales
								</Link>
							</div>
						</div>
					</div>

					<div className="text-center mt-12">
						<Link href="/project/new" className="text-gray-500 hover:underline block mb-4">
							Skip and continue for free
						</Link>
						<p className="text-gray-500">
							By {hadTrial ? 'subscribing' : 'starting your free trial'}, you agree to our{' '}
							<Link href="/terms" className="text-primary hover:underline">
								Terms of Service
							</Link>{' '}
							and{' '}
							<Link href="/privacy" className="text-primary hover:underline">
								Privacy Policy
							</Link>
							.
						</p>
					</div>
				</div>
			</div>
		</div>
	)
}
