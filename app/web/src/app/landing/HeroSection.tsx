import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import FloatingCard from './FloatingCard'
import GoogleIcon from './GoogleIcon'
import { ArrowRight, Check } from 'lucide-react'
import { Star } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

const HeroSection = () => {
	return (
		<section className="relative py-16 sm:py-20 md:py-28 px-4 overflow-hidden">
			{/* Background Elements */}
			<div className="absolute inset-0 overflow-hidden pointer-events-none z-0">
				<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-[#FFF5F1] rounded-full opacity-50"></div>
			</div>

			{/* Floating Cards - in a separate container with higher z-index */}
			<div className="absolute inset-0 overflow-hidden pointer-events-none z-10">
				{/* <FloatingCard 
          className="absolute top-[50%] left-[80%] animate-float"
          icon="🔍"
          text="New opportunity found"
        />
        
        <FloatingCard 
          className="absolute top-[25%] right-[15%] animate-float-slow"
          icon="📊"
          text="High engagement detected"
        />
        
        <FloatingCard 
          className="absolute bottom-[35%] left-[20%] animate-float-medium"
          icon="📈"
          text="Trending r/programming post"
        />
        
        <FloatingCard 
          className="absolute bottom-[25%] right-[10%] animate-float-slowest"
          icon="💬"
          text="AI draft ready"
        /> */}

				{/* <div className="floating-card absolute top-[45%] left-[25%] bg-white rounded-lg shadow-lg animate-float-medium">
          <div className="flex flex-col p-4">
            <div className="flex items-center mb-2">
              <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
              <div className="text-xs text-gray-600">Reddit Score</div>
            </div>
            <div className="font-bold text-xl text-gray-800">87%</div>
          </div>
        </div> */}
			</div>

			<div className="max-w-5xl mx-auto relative z-20">
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
					{/* Left Column - Hero Text */}
					<div>
						<div className="bg-[#fff1eb] text-[#FF4500] inline-block px-4 py-2 rounded-full text-sm font-medium mb-6">
							Stop missing opportunities
						</div>

						<h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
							Get Discovered on Reddit
							<span className="text-[#FF4500]"> — Automatically</span>
						</h1>

						<p className="text-lg text-gray-600 mb-6">
							We help small software startups get noticed on Reddit. Our AI finds high-impact posts in
							your niche and writes smart, ready-to-post replies — sent straight to your inbox.
						</p>

						<ul className="space-y-3 mb-8">
							{[
								'Continuous 24/7 Reddit monitoring',
								'AI-powered reply suggestions',
								'Immediate delivery to your inbox',
							].map((item, i) => (
								<li key={i} className="flex items-center">
									<div className="rounded-full bg-[#fff1eb] p-1 mr-3">
										<Check className="h-4 w-4 text-[#FF4500]" />
									</div>
									<span className="text-gray-700">{item}</span>
								</li>
							))}
						</ul>

						<div className="flex flex-col sm:flex-row items-center gap-4 mb-6">
							<Link href="/signup">
								<Button
									size="lg"
									className="flex items-center justify-center bg-[#FF4500] hover:bg-[#e03d00] shrink-0 w-full sm:w-auto text-white"
								>
									Get Early Access
									<ArrowRight className="h-5 w-5 ml-2" />
								</Button>
							</Link>
						</div>

						<div className="flex items-center gap-4 text-sm text-gray-500">
							<div className="flex -space-x-2">
								<div className="relative w-8 h-8">
									<Image
										src="/landing/assets/Ellipse-1.png"
										alt="Ellipse 1"
										width={32}
										height={32}
										priority
										className="object-contain"
									/>
								</div>
								<div className="relative w-8 h-8">
									<Image
										src="/landing/assets/Ellipse-2.png"
										alt="Ellipse 2"
										width={32}
										height={32}
										priority
										className="object-contain"
									/>
								</div>
								<div className="relative w-8 h-8">
									<Image
										src="/landing/assets/Ellipse-3.png"
										alt="Ellipse 3"
										width={32}
										height={32}
										priority
										className="object-contain"
									/>
								</div>
							</div>
							<div>Already placed 51k+ comments</div>
							<div className="flex">
								{[1, 2, 3, 4, 5].map((_, index) => (
									<Star key={index} className="h-4 w-4 text-yellow-400 fill-current" />
								))}
							</div>
						</div>
					</div>

					{/* Right Column - Demo Panel */}
					<div className="relative border border-gray-200 bg-white shadow-xl rounded-2xl p-6 hidden lg:block">
						<div className="flex items-center mb-4">
							<div className="bg-[#FF4500] w-10 h-10 rounded-full flex items-center justify-center text-white font-bold mr-3">
								<svg
									width="24"
									height="24"
									viewBox="0 0 24 24"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										className="fill-white"
										d="M153.71,134.72h45.26v-21.19h-45.26c-1.55,0-2.33-1.88-1.23-2.98l32-32-14.98-14.98-32,32c-1.1,1.1-2.98.32-2.98-1.23v-45.26h-21.19v45.26c0,1.55-1.88,2.33-2.98,1.23l-32-32-14.98,14.98,29.59,29.59h25.76c11.7,0,21.19,9.49,21.19,21.19v25.76l29.59,29.59,14.98-14.98-32-32c-1.1-1.1-.32-2.98,1.23-2.98Z"
										transform="scale(0.1) translate(0, 0)"
									/>
									<path
										className="fill-white"
										d="M110.49,121.51l-64.51,1.94.64,21.18,42.47-1.28-41.2,43.72,15.43,14.53,41.2-43.72,1.28,42.47,21.18-.64-1.94-64.51c-.23-7.8-6.75-13.93-14.54-13.69Z"
										transform="scale(0.1) translate(0, 0)"
									/>
								</svg>
							</div>
							<h3 className="font-bold">LeadsAutomatic</h3>
							<div className="ml-auto bg-[#fff1eb] text-[#FF4500] px-2 py-1 rounded text-xs font-medium">
								New opportunity
							</div>
						</div>

						<div className="border-t border-b border-gray-100 py-4 mb-4">
							<h4 className="font-medium mb-2">Trending on r/SideProject</h4>
							<p className="text-gray-600 text-sm mb-3">
								"Looking for a tool that can help me automate my social media posts for my new SaaS app.
								Any recommendations?"
							</p>
							<div className="flex items-center text-sm text-gray-500">
								<span>Posted 2h ago</span>
								<span className="mx-2">•</span>
								<span>15 comments</span>
								<span className="mx-2">•</span>
								<span>87% match</span>
							</div>
						</div>

						<div className="mb-4">
							<h4 className="font-medium mb-2">Suggested Reply:</h4>
							<div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-700">
								I built something to solve a similar problem for my own startup. For social automation,
								you might want to check out Buffer or Hootsuite. But if you're specifically looking for
								a Reddit-focused tool, my startup LeadsAutomatic might help. Instead of automated
								posting (which Reddit hates), it finds relevant conversations and suggests personalized
								replies. Happy to chat more about your specific needs!
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	)
}

export default HeroSection
