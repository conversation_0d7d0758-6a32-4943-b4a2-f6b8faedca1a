"use client"

import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import Footer from '../landing/Footer'
import { useEffect } from 'react'
export default function TermsPage() {
	return (
		<div className="max-w-4xl mx-auto px-4 py-16">
			<Link href="/">
				<Button variant="ghost" className="mb-8">
					<ArrowLeft className="h-4 w-4 mr-2" />
					Back to Home
				</Button>
			</Link>

			<h1 className="text-4xl font-bold mb-8">Terms of Service</h1>

			<div className="prose prose-gray max-w-none">
				<p className="text-gray-600 mb-8">Last updated: 10/05/2025</p>

				<h2 className="text-2xl font-semibold mb-4">1. Acceptance of Terms</h2>
				<p className="mb-6">
					By accessing and using LeadsAutomatic ("the Service") by PEWEO SARL, you agree to be bound by these Terms of Service
					and all applicable laws and regulations. If you do not agree with any of these terms, you are
					prohibited from using or accessing the Service.
				</p>

				<h2 className="text-2xl font-semibold mb-4">2. Use License</h2>
				<p className="mb-6">
					Permission is granted to temporarily use the Service for personal, non-commercial transitory viewing
					only. This is the grant of a license, not a transfer of title, and under this license you may not:
				</p>
				<ul className="list-disc pl-6 mb-6">
					<li>Modify or copy the materials</li>
					<li>Use the materials for any commercial purpose</li>
					<li>Attempt to decompile or reverse engineer any software contained in the Service</li>
					<li>Remove any copyright or other proprietary notations from the materials</li>
					<li>Transfer the materials to another person or "mirror" the materials on any other server</li>
				</ul>

				<h2 className="text-2xl font-semibold mb-4">3. User Responsibilities</h2>
				<p className="mb-6">
					When using LeadsAutomatic, you agree to:
				</p>
				<ul className="list-disc pl-6 mb-6">
					<li>Provide accurate and complete information</li>
					<li>Maintain the security of your account</li>
					<li>Comply with Reddit's terms of service and API usage guidelines</li>
					<li>Not use the service for any illegal or unauthorized purpose</li>
					<li>Not interfere with or disrupt the service or servers</li>
				</ul>

				<h2 className="text-2xl font-semibold mb-4">4. Service Limitations</h2>
				<p className="mb-6">
					LeadsAutomatic reserves the right to:
				</p>
				<ul className="list-disc pl-6 mb-6">
					<li>Modify or discontinue the service at any time</li>
					<li>Refuse service to anyone for any reason</li>
					<li>Limit the number of requests or actions per account</li>
					<li>Change pricing with notice to users</li>
				</ul>

				<h2 className="text-2xl font-semibold mb-4">5. Disclaimer</h2>
				<p className="mb-6">
					The materials on LeadsAutomatic are provided on an 'as is' basis. LeadsAutomatic makes no
					warranties, expressed or implied, and hereby disclaims and negates all other warranties including,
					without limitation, implied warranties or conditions of merchantability, fitness for a particular
					purpose, or non-infringement of intellectual property or other violation of rights.
				</p>

				<h2 className="text-2xl font-semibold mb-4">6. Limitations</h2>
				<p className="mb-6">
					In no event shall LeadsAutomatic or its suppliers be liable for any damages (including, without
					limitation, damages for loss of data or profit, or due to business interruption) arising out of the
					use or inability to use the Service.
				</p>

				<h2 className="text-2xl font-semibold mb-4">7. Revisions and Errata</h2>
				<p className="mb-6">
					The materials appearing on LeadsAutomatic could include technical, typographical, or photographic
					errors. LeadsAutomatic does not warrant that any of the materials on its website are accurate,
					complete, or current. LeadsAutomatic may make changes to the materials contained on its website at
					any time without notice.
				</p>

				<h2 className="text-2xl font-semibold mb-4">8. Contact Information</h2>
				<p className="mb-6">
					If you have any questions about these Terms of Service, please contact us at:
					<br />
					<a href="mailto:<EMAIL>" className="text-[#FF4500] hover:underline">
						<EMAIL>
					</a>
				</p>
			</div>

			<Footer />
		</div>
	)
} 