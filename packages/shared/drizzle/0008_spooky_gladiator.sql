CREATE TABLE "project_subreddit" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"project_id" uuid NOT NULL,
	"subreddit_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "project_subreddit" ADD CONSTRAINT "project_subreddit_project_id_project_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."project"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "project_subreddit" ADD CONSTRAINT "project_subreddit_subreddit_id_subreddit_id_fk" FOREIGN KEY ("subreddit_id") REFERENCES "public"."subreddit"("id") ON DELETE cascade ON UPDATE no action;