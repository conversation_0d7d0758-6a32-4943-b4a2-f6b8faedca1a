import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

const FinalCTA = () => {
	return (
		<section id="early-access" className="py-16 bg-gray-900 text-white">
			<div className="max-w-4xl mx-auto px-4 text-center">
				<h2 className="text-3xl sm:text-4xl font-bold mb-6">Ready to get discovered on Reddit?</h2>
				<p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
					Join the waitlist today and be among the first to access LeadsAutomatic when we launch.
				</p>

				<div className="max-w-md mx-auto">
					<form className="flex flex-col sm:flex-row gap-4 mb-6">
						<Input
							type="email"
							placeholder="Enter your email"
							className="flex-grow px-4 py-6 rounded-lg text-gray-900"
							required
						/>
						<Button
							type="submit"
							className="px-6 py-6 bg-[#FF4500] hover:bg-[#e03d00] font-medium rounded-lg transition-colors whitespace-nowrap"
						>
							Get Early Access
						</Button>
					</form>
					<p className="text-sm text-gray-400">No credit card required. We'll notify you when we're ready.</p>
				</div>
			</div>
		</section>
	)
}

export default FinalCTA
