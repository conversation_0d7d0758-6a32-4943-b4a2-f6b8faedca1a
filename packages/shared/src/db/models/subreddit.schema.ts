import {
	pgTable,
	uuid,
	varchar,
	text,
	timestamp,
	integer,
	boolean,
	uniqueIndex,
	vector,
	index,
} from 'drizzle-orm/pg-core'

export const subreddit = pgTable(
	'subreddit',
	{
		// Primary identifiers
		id: uuid('id').defaultRandom().primary<PERSON>ey(),
		redditId: varchar('reddit_id', { length: 10 }).notNull().unique(), // e.g. "2qs0k"
		name: varchar('name', { length: 50 }).notNull(), // e.g. "t5_2qs0k"
		displayName: varchar('display_name', { length: 100 }).notNull(), // e.g. "Home"
		prefixedName: varchar('prefixed_name', { length: 102 }).notNull(), // e.g. "r/Home"
		url: varchar('url', { length: 255 }).notNull(), // e.g. "/r/Home/"

		// Content
		title: varchar('title', { length: 255 }).notNull(),
		description: text('description').notNull(),
		descriptionHtml: text('description_html'),
		publicDescription: text('public_description'),
		publicDescriptionHtml: text('public_description_html'),

		// Stats
		subscribers: integer('subscribers'),
		activeUserCount: integer('active_user_count'),

		// Type information
		kind: varchar('kind', { length: 10 }),
		subredditType: varchar('subreddit_type', { length: 50 }),
		over18: boolean('over18').default(false),

		// Features
		allowImages: boolean('allow_images'),
		allowVideos: boolean('allow_videos'),
		allowGalleries: boolean('allow_galleries'),
		restrictPosting: boolean('restrict_posting'),

		// Icons and images
		iconImg: text('icon_img'),
		communityIcon: text('community_icon'),
		bannerImg: text('banner_img'),

		// Timestamps
		createdAt: timestamp('created_at').defaultNow().notNull(),
		updatedAt: timestamp('updated_at').defaultNow().notNull(),
		redditCreatedUtc: timestamp('reddit_created_utc'),

		embedding: vector('embedding', { dimensions: 1536 }),
	},
	(table: any) => [
		uniqueIndex('reddit_id_idx').on(table.redditId),
		index('embeddingIndex').using('hnsw', table.embedding.op('vector_cosine_ops')),
	],
)
