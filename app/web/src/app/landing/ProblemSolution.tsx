const ProblemSolution = () => {
	const problems = [
		{
			image: '/avatar-1.png',
			problem: (
				<div>
					<span className="font-medium">Monthly subscriptions for </span>
					<span className="text-blue-500 font-medium">Buffer</span>
					<span className="font-medium">, </span>
					<span className="font-medium">Hootsuite</span>
					<span className="font-medium">, </span>
					<span className="text-[#FF4500] font-medium">Reddit Ads</span>
					<span className="font-medium"> eat up </span>
					<span className="text-[#FF4500] font-medium">$500+/month</span>.
				</div>
			),
		},
		{
			image: '/avatar-2.png',
			problem: (
				<div>
					<span className="font-medium">Switching between: </span>
					<span className="font-medium">ChatGPT</span>
					<span className="font-medium">, </span>
					<span className="font-medium">Jasper</span>
					<span className="font-medium">, </span>
					<span className="font-medium">Reddit</span>
					<span className="text-[#FF4500] font-medium"> - losing time and efficiency</span>.
				</div>
			),
		},
		{
			image: '/avatar-3.png',
			problem: (
				<div>
					<span className="font-medium">Hours spent </span>
					<span className="text-[#FF4500] font-medium">learning Reddit etiquette and monitoring posts </span>
					<span className="font-medium">instead of growing business.</span>
				</div>
			),
		},
	]

	return (
		<section className="py-20 border-b border-gray-100">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-10">
					<p className="text-gray-500 uppercase tracking-wider text-sm mb-2">PROBLEMS & SOLUTION</p>

					<div className="relative">
						<h2 className="text-4xl md:text-5xl font-bold mb-2">
							Your problem
							<br />
							<span className="relative inline-block">Our solution</span>
						</h2>
					</div>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
					{/* Left column - problems */}
					<div className="space-y-6">
						{problems.map((item, index) => (
							<div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
								<div className="flex items-center">
									<div className="w-8 h-8 rounded-full bg-gray-200 mr-4 overflow-hidden flex-shrink-0 flex items-center justify-center">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="16"
											height="16"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
											strokeLinecap="round"
											strokeLinejoin="round"
											className="text-[#FF4500]"
										>
											<path d="M12 5v14" />
											<path d="m19 12-7 7-7-7" />
										</svg>
									</div>
									<div className="text-gray-800">{item.problem}</div>
								</div>
							</div>
						))}
					</div>

					{/* Right column - solution */}
					<div className="bg-[#FF4500] rounded-2xl p-8 text-white shadow-xl">
						<div className="flex items-center mb-6">
							<div className="bg-white w-10 h-10 rounded-lg flex items-center justify-center mr-3">
								<svg
									width="24"
									height="24"
									viewBox="0 0 24 24"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										className="logo-orange"
										d="M153.71,134.72h45.26v-21.19h-45.26c-1.55,0-2.33-1.88-1.23-2.98l32-32-14.98-14.98-32,32c-1.1,1.1-2.98.32-2.98-1.23v-45.26h-21.19v45.26c0,1.55-1.88,2.33-2.98,1.23l-32-32-14.98,14.98,29.59,29.59h25.76c11.7,0,21.19,9.49,21.19,21.19v25.76l29.59,29.59,14.98-14.98-32-32c-1.1-1.1-.32-2.98,1.23-2.98Z"
										transform="scale(0.1) translate(0, 0)"
									/>
									<path
										className="logo-orange"
										d="M110.49,121.51l-64.51,1.94.64,21.18,42.47-1.28-41.2,43.72,15.43,14.53,41.2-43.72,1.28,42.47,21.18-.64-1.94-64.51c-.23-7.8-6.75-13.93-14.54-13.69Z"
										transform="scale(0.1) translate(0, 0)"
									/>
								</svg>
							</div>
							<h3 className="text-xl font-bold">Leads Automatic</h3>
						</div>

						<p className="text-lg mb-6">Replace multiple tools with one powerful platform:</p>

						<ul className="space-y-3">
							{[
								'Subreddit Monitoring',
								'Opportunity Detection',
								'AI Reply Generation',
								'Voice Matching',
								'Instant Delivery',
							].map((feature, index) => (
								<li key={index} className="flex items-center">
									<svg
										className="w-5 h-5 mr-3 text-white"
										fill="currentColor"
										viewBox="0 0 20 20"
										xmlns="http://www.w3.org/2000/svg"
									>
										<path
											fillRule="evenodd"
											d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
											clipRule="evenodd"
										></path>
									</svg>
									<span>{feature}</span>
								</li>
							))}
						</ul>
					</div>
				</div>
			</div>
		</section>
	)
}

export default ProblemSolution
