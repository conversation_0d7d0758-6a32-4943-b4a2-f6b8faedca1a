'use server'

import { openai } from '@packages/shared'

// Define the response type
interface WebsiteContentResponse {
	success: boolean
	description?: string
	painPoint?: string
	keywords?: string[]
	primaryKeyword?: string
	error?: string
}

/**
 * Fetches website content using Jina API and generates product description and pain point using OpenAI
 * @param url The website URL to analyze
 */
export async function fetchWebsiteContent(url: string): Promise<WebsiteContentResponse> {
	if (!openai || !openai?.ai) {
		return {
			success: false,
			error: 'OpenAI is not set',
		}
	}
	try {
		// Ensure URL has proper format
		let formattedUrl = url
		if (!url.startsWith('http://') && !url.startsWith('https://')) {
			formattedUrl = `https://${url}`
		}

		// Fetch website content using Jina API
		const jinaUrl = `https://r.jina.ai/${encodeURIComponent(formattedUrl)}`
		console.log(`Fetching website content from: ${jinaUrl}`)

		const response = await fetch(jinaUrl)

		if (!response.ok) {
			console.error(`Failed to fetch website content: ${response.status} ${response.statusText}`)
			return {
				success: false,
				error: `Failed to fetch website content: ${response.status} ${response.statusText}`,
			}
		}

		// Get the website content
		const websiteContent = await response.text()

		if (!websiteContent || websiteContent.trim() === '') {
			return { success: false, error: 'No content found on the website' }
		}

		// Process with OpenAI to generate description, pain point, and keywords
		// Create a prompt for OpenAI
		const prompt = `
    I have the following website content. Please analyze it and provide:
    1. A concise product description (max 500 characters) that explains what the product does and who it's for
    2. A product pain point (max 500 characters) that describes the problem this product solves
    3. A list of 3-5 highly targeted keywords that are:
       - Specific to the product's core functionality and value proposition
       - Commonly used in relevant Reddit discussions
       - Include both technical terms and user-focused phrases
       - Avoid generic terms and focus on unique selling points
       - Prioritize terms that would help find relevant discussionxws where the product could provide value
	4. Pick a primary keyword that is the most relevant to the product and that can find relvent subreddits

    Website content:
    ${websiteContent.substring(0, 10000)} // Limit content to avoid token limits

    Format your response as JSON with three fields: "description", "painPoint", "keywords" (as an array of strings) and "primaryKeyword" (as a string)
    `

		// Call OpenAI API
		const completion = await openai?.ai?.chat?.completions.create({
			model: 'gpt-3.5-turbo',
			messages: [
				{
					role: 'system',
					content:
						'You are a helpful assistant that analyzes website content and extracts product information.',
				},
				{ role: 'user', content: prompt },
			],
			response_format: { type: 'json_object' },
		})

		// Parse the response
		const content = completion.choices[0].message.content
		if (!content) {
			return { success: false, error: 'Failed to generate content from OpenAI' }
		}

		try {
			const parsedContent = JSON.parse(content)
			return {
				success: true,
				description: parsedContent.description,
				painPoint: parsedContent.painPoint,
				keywords: parsedContent.keywords || [],
				primaryKeyword: parsedContent.primaryKeyword,
			}
		} catch (error) {
			console.error('Error parsing OpenAI response:', error)
			return { success: false, error: 'Failed to parse OpenAI response' }
		}
	} catch (error) {
		console.error('Error in fetchWebsiteContent:', error)
		return {
			success: false,
			error: error instanceof Error ? error.message : 'An unknown error occurred',
		}
	}
}
