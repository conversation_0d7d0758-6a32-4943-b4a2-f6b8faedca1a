FROM node:22-alpine AS base
WORKDIR /app

# Build stage for production
FROM base AS development
COPY package.json ./
COPY yarn.lock ./
RUN yarn install
CMD ["yarn", "dev"]

FROM peweo/automatic-shared:prod AS shared-build
WORKDIR /app

# Build stage for production
FROM node:22-alpine AS build
WORKDIR /app
COPY package.json /app/package.json
RUN yarn install --frozen-lockfile
COPY . /app
COPY --from=shared-build /app/ ./node_modules/@packages/shared/
ENV NODE_ENV=production
RUN yarn build

# Production stage
FROM node:22-alpine AS production
WORKDIR /app
COPY --from=build /app/.next/standalone ./
COPY --from=build /app/.next/static/ ./.next/static
COPY --from=build /app/public/ ./public
EXPOSE 3000
CMD ["node", "server.js"]
