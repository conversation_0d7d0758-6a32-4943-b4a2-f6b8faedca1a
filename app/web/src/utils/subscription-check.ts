'use client'

import { hasActiveSubscription } from '@/actions/subscription'
import { useRouter } from 'next/navigation'

/**
 * Utility function to check subscription status and handle redirects
 * @param userId User ID to check subscription for
 * @param router Next.js router instance
 * @returns Promise that resolves to true if subscription is active, false otherwise
 */
export async function checkSubscriptionStatus(userId: string, router: any): Promise<boolean> {
  try {
    const result = await hasActiveSubscription(userId)

    if (!result.success) {
      // Handle error
      // router.push('/plans')
      return false
    }

    // If payment update is required (past_due status), redirect to billing page
    if (result.paymentUpdateRequired) {
      router.push('/billing')
      return false
    }

    // If no active subscription, redirect to plans page
    if (!result.hasSubscription) {
      // router.push('/plans')
      return false
    }

    return true
  } catch (err) {
    console.error('Error checking subscription:', err)
    // router.push('/plans')
    return false
  }
}
