{"name": "@packages/shared", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "yarn migrate && find src -name '*.ts' | xargs esbuild --outdir=dist --format=cjs --platform=node --watch=forever", "generate": "drizzle-kit generate --config=src/drizzle.config.ts", "migrate": "drizzle-kit migrate --config=src/drizzle.config.ts", "generate:custom": "drizzle-kit generate --config=src/drizzle.config.ts --custom"}, "devDependencies": {"@types/node": "^22.14.1", "@types/pg": "^8.11.13", "esbuild-register": "3.6.0", "typescript": "^5.0.0"}, "dependencies": {"axios": "^1.9.0", "bullmq": "^5.49.2", "drizzle-kit": "^0.31.0", "drizzle-orm": "^0.43.1", "esbuild": "^0.25.2", "openai": "^4.95.1", "pg": "^8.14.1", "postgres": "^3.4.5", "zod": "^3.24.1"}, "peerDependencies": {"drizzle-orm": "^0.43.1"}}