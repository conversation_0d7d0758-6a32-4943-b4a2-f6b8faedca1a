/// <reference types="cypress" />

// Import commands.js using ES2015 syntax:
import '@testing-library/cypress/add-commands'

// Firebase authentication commands
Cypress.Commands.add('login', (email: string, password: string) => {
	cy.session([email, password], () => {
		cy.visit('/login')
		cy.get('input[type="email"]').type(email)
		cy.get('input[type="password"]').type(password)
		cy.get('button[type="submit"]').click()

		// Wait for redirect to project/new page
		cy.url().should('include', '/project/new')
	})
})

// Custom command to handle Firebase auth state
Cypress.Commands.add('logout', () => {
	cy.window().then((win) => {
		return win.eval(`
      if (window.firebase && window.firebase.auth) {
        window.firebase.auth().signOut();
      }
    `)
	})

	// Clear cookies
	cy.clearCookies()
	cy.clearLocalStorage()
})

// Custom command to check for validation messages
Cypress.Commands.add('checkValidationMessage', (message: string) => {
	// First try to find the message in an alert
	cy.get('.alert').then(($alert) => {
		if ($alert.length && $alert.text().includes(message)) {
			cy.wrap($alert).should('contain.text', message)
		} else {
			// If not found in an alert, look for it anywhere in the document
			cy.contains(message).should('be.visible')
		}
	})
})

// Declare global Cypress namespace to add custom commands
declare global {
	namespace Cypress {
		interface Chainable {
			login(email: string, password: string): Chainable<void>
			logout(): Chainable<void>
			checkValidationMessage(message: string): Chainable<void>
		}
	}
}
