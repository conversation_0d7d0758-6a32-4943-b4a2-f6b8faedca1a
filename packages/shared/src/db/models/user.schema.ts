import { pgTable, varchar, timestamp, uniqueIndex } from 'drizzle-orm/pg-core'

export const user = pgTable(
	'user',
	{
		// Primary identifier - using Firebase UID as the primary key
		id: varchar('id', { length: 128 }).primaryKey(),
		email: varchar('email', { length: 255 }).notNull().unique(),

		// Timestamps
		createdAt: timestamp('created_at').defaultNow().notNull(),
		updatedAt: timestamp('updated_at').defaultNow().notNull(),
	},
	(table) => [uniqueIndex('email_idx').on(table.email)],
)
