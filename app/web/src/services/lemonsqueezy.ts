/**
 * Lemonsqueezy API integration
 */

// API base URL
const LEMONSQUEEZY_API_URL = 'https://api.lemonsqueezy.com/v1'

/**
 * Create a checkout URL for a specific variant
 * @param variantId The Lemonsqueezy variant ID
 * @param customerEmail The customer's email address
 * @param customData Custom data to pass to the checkout
 * @returns The checkout URL
 */
export async function createCheckout(
	variantId: string,
	customerEmail: string,
	customData: Record<string, any> = {},
): Promise<{ success: boolean; url?: string; error?: string }> {
	try {
		if (!process.env.LEMONSQUEEZY_API_KEY) {
			throw new Error('LEMONSQUEEZY_API_KEY is not defined')
		}

		const response = await fetch(`${LEMONSQUEEZY_API_URL}/checkouts`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json',
				Authorization: `Bearer ${process.env.LEMONSQUEEZY_API_KEY}`,
			},
			body: JSON.stringify({
				data: {
					type: 'checkouts',
					attributes: {
						variant_id: parseInt(variantId),
						custom_price: null,
						product_options: {
							redirect_url: process.env.LEMONSQUEEZY_REDIRECT_URL || '',
							receipt_button_text: 'Return to Dashboard',
							receipt_link_url: process.env.LEMONSQUEEZY_RECEIPT_URL || '',
							receipt_thank_you_note: 'Thank you for your purchase!',
							enabled_variants: [parseInt(variantId)],
						},
						checkout_options: {
							embed: false,
							media: true,
							logo: true,
							desc: true,
							discount: true,
							dark: false,
							subscription_preview: true,
							button_color: '#4f46e5',
						},
						checkout_data: {
							email: customerEmail,
							custom: customData,
						},
						expires_at: null,
					},
				},
			}),
		})

		if (!response.ok) {
			const errorData = await response.json()
			throw new Error(errorData.message || 'Failed to create checkout')
		}

		const data = await response.json()
		return {
			success: true,
			url: data.data.attributes.url,
		}
	} catch (error: any) {
		console.error('Error creating Lemonsqueezy checkout:', error)
		return {
			success: false,
			error: error.message || 'Failed to create checkout',
		}
	}
}
