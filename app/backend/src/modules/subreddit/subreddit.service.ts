import { db, schema, openai } from '@packages/shared'
import axios from 'axios'
import { and, desc, eq, gte, isNull } from 'drizzle-orm'
import { config } from '../../config'
import { redditApiRequest } from '@packages/shared'

// Check if Reddit credentials are available
const hasRedditCredentials = config.reddit.clientId && config.reddit.clientSecret

// Type for Reddit API response
interface RedditListingResponse {
	data: {
		children: Array<{
			kind: string
			data: any
		}>
		after: string | null
	}
}

export const subredditsToDb = async () => {
	try {
		const endpoints = ['/subreddits/popular', '/subreddits/default', '/subreddits/new']

		const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

		let processedCount = 0

		// Process each endpoint
		for (const endpoint of endpoints) {
			let after: string | null = null
			let hasMore = true

			// Paginate through all results
			while (hasMore) {
				// Add pagination parameter if we have an 'after' value
				const queryParams = after ? `?limit=100&after=${after}` : '?limit=100'
				const url = `${endpoint}${queryParams}`

				console.log(`Fetching from ${url}`)

				let response: RedditListingResponse
				// Use authenticated API if credentials are available, otherwise fallback to public API
				if (hasRedditCredentials) {
					// Use authenticated Reddit API
					response = await redditApiRequest(url, config.reddit.clientId, config.reddit.clientSecret, {
						method: 'GET',
					})
				} else {
					// Fallback to public API
					const axiosResponse = await axios.get(`https://www.reddit.com${url}.json`, {
						headers: { 'User-Agent': 'leadsautomatic/1.0 (by /u/f4yis)' },
					})
					response = axiosResponse.data
				}

				const subreddits = response.data.children

				// Break if no subreddits returned
				if (!subreddits || subreddits.length === 0) {
					hasMore = false
					continue
				}

				// Process each subreddit
				for (const item of subreddits) {
					const subredditData = item.data

					const existingSubreddit = await db.query.subreddit.findFirst({
						where: eq(schema.subreddit.redditId as any, subredditData.id) as any,
					})

					if (!existingSubreddit) {
						await db.insert(schema.subreddit).values({
							redditId: subredditData.id,
							name: subredditData.name,
							displayName: subredditData.display_name,
							prefixedName: subredditData.display_name_prefixed,
							url: subredditData.url,

							// Content
							title: subredditData.title,
							description: subredditData.description,
							descriptionHtml: subredditData.description_html,
							publicDescription: subredditData.public_description,
							publicDescriptionHtml: subredditData.public_description_html,

							// Stats
							subscribers: subredditData.subscribers,
							activeUserCount: subredditData.active_user_count,

							// Type information
							kind: item.kind,
							subredditType: subredditData.subreddit_type,
							over18: subredditData.over18,

							// Features
							allowImages: subredditData.allow_images,
							allowVideos: subredditData.allow_videos,
							allowGalleries: subredditData.allow_galleries,
							restrictPosting: subredditData.restrict_posting,

							// Icons and images
							iconImg: subredditData.icon_img,
							communityIcon: subredditData.community_icon,
							bannerImg: subredditData.banner_img,

							redditCreatedUtc: subredditData.reddit_created_utc,
						})
						processedCount++
					} else {
						// Update existing subreddit stats
						await db
							.update(schema.subreddit)
							.set({
								subscribers: subredditData.subscribers,
								activeUserCount: subredditData.active_user_count,
								// Add other fields you want to keep updated
							})
							.where(eq(schema.subreddit.redditId as any, subredditData.id))
					}
				}

				// Get pagination token for next page
				after = response.data.after

				// If no 'after' token, we've reached the end
				if (!after) {
					hasMore = false
				}

				// Respect rate limits - delay between requests
				await delay(2000) // 2 second delay between requests
			}

			// Additional delay between different endpoints
			await delay(5000) // 5 second delay between endpoints
		}

		console.log(`Successfully processed ${processedCount} new subreddits`)
	} catch (error) {
		console.error('Error fetching subreddits:', error)
	}
}

export const addEmbeddings = async () => {
	while (true) {
		const subreddits = await db.query.subreddit.findMany({
			limit: 150,
			orderBy: [desc(schema.subreddit.subscribers)],
			where: and(isNull(schema.subreddit.embedding), gte(schema.subreddit.subscribers, 10000)),
		})
		if (!subreddits.length) {
			console.log('No subreddits to add embeddings to')
			break
		}
		for (const subreddit of subreddits) {
			const string = `${subreddit.displayName} ${subreddit.title} ${subreddit.description}`
			await db
				.update(schema.subreddit)
				.set({
					embedding: await openai.generateEmbedding(string),
				})
				.where(eq(schema.subreddit.id, subreddit.id))
		}
	}
}
