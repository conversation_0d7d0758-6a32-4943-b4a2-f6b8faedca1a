'use server'

import { db, schema } from '@packages/shared'
import { eq } from 'drizzle-orm'

interface CreateUserParams {
	firebaseUid: string
	email: string
}

export async function createUser(params: CreateUserParams) {
	try {
		// Check if user already exists
		const existingUser = await db.query.user.findFirst({
			where: eq(schema.user.id, params.firebaseUid),
		})

		if (existingUser) {
			// Update existing user
			await db
				.update(schema.user)
				.set({
					email: params.email,
					updatedAt: new Date(),
				})
				.where(eq(schema.user.id, params.firebaseUid))

			return { success: true, id: existingUser.id }
		} else {
			// Create new user
			const result = await db
				.insert(schema.user)
				.values({
					id: params.firebaseUid,
					email: params.email,
				})
				.returning({ id: schema.user.id })

			return { success: true, id: result[0].id }
		}
	} catch (error) {
		console.error('Error creating/updating user:', error)
		return { success: false, error }
	}
}
