import { pgTable, uuid, varchar, text, timestamp, integer, boolean } from 'drizzle-orm/pg-core'
import { user } from './user.schema'
import { subreddit } from './subreddit.schema'

export const project = pgTable('project', {
	// Primary identifier
	id: uuid('id').defaultRandom().primaryKey(),

	// User reference - who created this project
	userId: varchar('user_id', { length: 128 })
		.notNull()
		.references(() => user.id),

	// Project details
	name: varchar('name', { length: 255 }).notNull(),
	url: varchar('url', { length: 255 }).notNull(),
	description: text('description').notNull(),
	painPoint: text('pain_point').notNull(),
	businessType: varchar('business_type', { length: 10 }).notNull(), // B2B or B2C
	averageSaleValue: integer('average_sale_value').notNull(),
	keywords: text('keywords').default('').notNull(),
	primaryKeyword: varchar('primary_keyword', { length: 255 }),
	aiEnabled: boolean('ai_enabled').default(true).notNull(),

	lastScanCompleted: timestamp('last_scan_completed'),

	// Timestamps
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull(),
})

// Join table for the many-to-many relationship between projects and subreddits
export const projectSubreddit = pgTable('project_subreddit', {
	id: uuid('id').defaultRandom().primaryKey(),
	projectId: uuid('project_id')
		.notNull()
		.references(() => project.id, { onDelete: 'cascade' }),
	subredditId: uuid('subreddit_id')
		.notNull()
		.references(() => subreddit.id, { onDelete: 'cascade' }),
	selected: boolean('selected').default(false).notNull(),
	aiSuggested: boolean('ai_suggested').default(false).notNull(),
	createdAt: timestamp('created_at').defaultNow().notNull(),
})
