name: Build and Push Docker Compose Images

on:
  push:
    branches:
      - staging

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    environment: staging
    steps:
      # Step 1: Check out the repository
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          ref: staging

      # Step 2: Log in to Docker Hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      # Step 3: Build Docker Compose services
      - name: Build Docker Compose services
        run: |
          docker compose build --no-cache

      # Step 4: Push Docker Compose images
      - name: Push Docker Compose images
        run: |
          touch env/.env.backend
          touch env/.env.web
          touch env/.env.shared
          touch .env
          docker compose push --ignore-push-failures

      # Step 5: Log out from Docker Hub
      - name: Log out from Docker Hub
        run: docker logout

      - name: Deploy
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.HOSTNAME }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.KEY }}
          port: ${{ secrets.PORT }}
          command_timeout: 30m
          script: | 
            cd /root/cribs
            docker compose pull
            docker compose up -d