services:
  shared:
    image: peweo/automatic-shared:dev
    build:
      context: ./packages/shared
      target: development
      dockerfile: Dockerfile
    volumes:
      - ./packages/shared:/app
      - shared:/app/dist
      - /app/node_modules
    networks:
      - automatic-net
    env_file:
      - .env
      - ./env/.env.shared
    depends_on:
      db:
        condition: service_healthy
  web:
    image: peweo/automatic-fe:dev
    build:
      context: ./app/web
      target: development
      dockerfile: Dockerfile
    stdin_open: true
    volumes:
      - ./app/web:/app
      - shared:/app/node_modules/@packages/shared
      - /app/node_modules
      - ~/.cache/yarn:/usr/local/share/.cache/yarn
    restart: no
    ports:
      - 3000:3000
    networks:
      - automatic-net
    env_file:
      - env/.env.web
      - .env
      - ./env/.env.shared
    depends_on:
      db:
        condition: service_healthy
      shared:
        condition: service_started
      redis:
        condition: service_started
  backend:
    image: peweo/automatic-be:dev
    restart: no
    build:
      context: ./app/backend
      target: development
      dockerfile: Dockerfile
    volumes:
      - ./app/backend:/app
      - shared:/app/node_modules/@packages/shared
      - /app/node_modules
      - ~/.cache/yarn:/usr/local/share/.cache/yarn
    develop:
      watch:
        - action: rebuild
          path: ./app/backend/package.json
    networks:
      - automatic-net
    ports:
      - 8080:8080
      - 9229:9229
    env_file:
      - ./env/.env.backend
      - .env
      - ./env/.env.shared
    depends_on:
      db:
        condition: service_healthy
      shared:
        condition: service_started
      redis:
        condition: service_started
    command: [ "npm", "run", "dev" ]
  db:
    image: pgvector/pgvector:pg17
    platform: linux/x86_64
    restart: always
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    volumes:
      - ./data/db:/var/lib/postgresql/data
    ports:
      - 127.0.0.1:5432:5432
    networks:
      - automatic-net
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_DATABASE} -h localhost || exit 1"]
      start_period: 10s
      interval: 60s
      timeout: 5s
      retries: 10
  redis:
    image: redis:latest
    networks:
      - automatic-net
networks:
  automatic-net:


volumes:
  shared:
