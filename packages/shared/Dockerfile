FROM node:22-alpine AS base
WORKDIR /app
COPY package.json ./
COPY yarn.lock ./

FROM base AS development
WORKDIR /app
RUN yarn install
CMD ["yarn", "dev"]

FROM base AS build
WORKDIR /app
RUN yarn install
COPY . .
RUN yarn build

# Production stage
FROM node:22-alpine AS production
WORKDIR /app
COPY --from=build /app/package*.json ./
COPY --from=build /app/dist ./dist
COPY --from=build /app/src/drizzle.config.ts /core/src/drizzle.config.ts
COPY --from=build /app/drizzle /core/drizzle
COPY --from=build /app/node_modules /core/node_modules
COPY --from=build /app/package.json /core/package.json
WORKDIR /core
CMD ["yarn", "migrate"]
