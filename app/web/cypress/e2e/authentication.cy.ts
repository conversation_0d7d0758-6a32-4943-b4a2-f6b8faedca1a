describe('Authentication', () => {
	// Test user credentials - you should use test accounts for this
	const testUser = {
		email: '<EMAIL>',
		password: '123fayis',
		fullName: 'Test User',
	}

	beforeEach(() => {
		// Clear cookies and localStorage before each test
		cy.clearCookies()
		cy.clearLocalStorage()
		cy.visit('/')
	})

	it('should navigate to login page', () => {
		cy.get('a').contains('Login').click()
		cy.url().should('include', '/login')
		cy.get('form').should('be.visible')
		cy.get('input[type="email"]').should('be.visible')
		cy.get('input[type="password"]').should('be.visible')
		cy.get('button[type="submit"]').should('be.visible')
	})

	it('should navigate to signup page', () => {
		cy.get('a').contains('Sign Up').click()
		cy.url().should('include', '/signup')
		cy.get('form').should('be.visible')
		cy.get('input[type="email"]').should('be.visible')
		cy.get('input[type="password"]').should('be.visible')
		cy.get('button[type="submit"]').should('be.visible')
	})

	it('should show validation errors on login form', () => {
		cy.visit('/login')
		// Clear any existing values
		cy.get('input[type="email"]').clear()
		cy.get('input[type="password"]').clear()
		// Submit the form
		cy.get('button[type="submit"]').click()
		// Check for the validation message
		cy.checkValidationMessage('Please fill in all fields')
	})

	it('should show validation errors on signup form', () => {
		cy.visit('/signup')
		// Clear any existing values
		cy.get('input[type="email"]').clear()
		cy.get('input[type="password"]').first().clear()
		cy.get('input[type="password"]').last().clear()
		// Submit the form
		cy.get('button[type="submit"]').click()
		// Check for the validation message
		cy.checkValidationMessage('Please fill in all fields')

		// Test password mismatch
		cy.get('input[type="email"]').type(testUser.email)
		cy.get('input[type="password"]').first().type(testUser.password)
		cy.get('input[type="password"]')
			.last()
			.type(testUser.password + '1')
		cy.get('button[type="submit"]').click()
		cy.checkValidationMessage('Passwords do not match')
	})

	it('should redirect to project/new after successful login', () => {
		cy.visit('/login')
		cy.get('input[type="email"]').type(testUser.email)
		cy.get('input[type="password"]').type(testUser.password)
		cy.get('button[type="submit"]').click()

		// This assumes the login will succeed with the test credentials
		// In a real test, you might want to use a test account or mock the Firebase auth
		cy.url().should('include', '/project/new', { timeout: 30000 })
	})

	it('should redirect to login page when accessing protected route without authentication', () => {
		// Clear any existing cookies to ensure we're not authenticated
		cy.clearCookies()
		cy.clearLocalStorage()

		// Visit the protected route
		cy.visit('/project/new', {
			// Don't fail on status code or app exceptions
			failOnStatusCode: false,
			onBeforeLoad(win) {
				// Listen for redirect events
				cy.spy(win, 'addEventListener').as('listener')
			},
		})

		// Add a longer timeout and retry
		cy.url().should('include', '/login', { timeout: 30000 })
	})

	it('should allow user to logout', () => {
		// First login
		cy.visit('/login')
		cy.get('input[type="email"]').type(testUser.email)
		cy.get('input[type="password"]').type(testUser.password)
		cy.get('button[type="submit"]').click()

		// Wait for redirect to project/new page
		cy.url().should('include', '/project/new', { timeout: 30000 })

		// Click on the avatar to open dropdown
		cy.get('.avatar').click()

		// Click logout button
		cy.contains('Logout').click()

		// Should redirect to home page
		cy.url().should('eq', Cypress.config().baseUrl + '/')
	})
})

// Firebase Authentication Mocking (if needed)
// describe('Authentication with Firebase Mocking', () => {
//   beforeEach(() => {
//     // Stub Firebase auth methods
//     cy.window().then((win) => {
//       // Create a stub for Firebase auth
//       (win as any).firebase = {
//         auth: () => ({
//           signInWithEmailAndPassword: cy.stub().resolves({
//             user: {
//               uid: 'test-uid',
//               email: '<EMAIL>',
//               displayName: 'Test User',
//               getIdToken: cy.stub().resolves('fake-token'),
//               getIdTokenResult: cy.stub().resolves({
//                 claims: { dbId: 'test-db-id' },
//                 expirationTime: new Date(Date.now() + 3600000).toISOString()
//               })
//             }
//           }),
//           createUserWithEmailAndPassword: cy.stub().resolves({
//             user: {
//               uid: 'test-uid',
//               email: '<EMAIL>',
//               displayName: 'Test User',
//               getIdToken: cy.stub().resolves('fake-token'),
//               getIdTokenResult: cy.stub().resolves({
//                 claims: { dbId: 'test-db-id' },
//                 expirationTime: new Date(Date.now() + 3600000).toISOString()
//               })
//             }
//           }),
//           signOut: cy.stub().resolves(),
//           onAuthStateChanged: cy.stub(),
//           onIdTokenChanged: cy.stub()
//         })
//       }
//     })
//   })

//   it('should mock Firebase login', () => {
//     cy.visit('/login')
//     cy.get('input[type="email"]').type('<EMAIL>')
//     cy.get('input[type="password"]').type('password')
//     cy.get('button[type="submit"]').click()

//     // Since we're mocking, we need to manually navigate
//     cy.visit('/project/new')
//     cy.url().should('include', '/project/new')
//   })
// })
