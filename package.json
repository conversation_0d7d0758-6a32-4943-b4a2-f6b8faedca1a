{"name": "automatic", "private": true, "scripts": {"dev:build": "docker compose -f compose.dev.yml up --build", "dev": "docker compose -f compose.dev.yml up", "migrate": "docker compose -f compose.dev.yml run shared yarn migrate", "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe", "yarn-install": "yarn install && yarn install --cwd app/web && yarn install --cwd app/backend && yarn install --cwd packages/shared"}, "dependencies": {"@biomejs/biome": "^1.9.4"}, "workspaces": ["app/web", "app/backend", "packages/shared"]}