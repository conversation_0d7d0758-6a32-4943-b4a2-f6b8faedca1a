CREATE TABLE "post" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"project_id" uuid,
	"reddit_id" varchar,
	"title" varchar NOT NULL,
	"author" varchar,
	"subreddit" varchar,
	"permalink" varchar,
	"url" varchar,
	"selftext" text,
	"created" timestamp,
	"score" integer,
	"num_comments" integer,
	"thumbnail" varchar,
	"is_original_content" boolean,
	"is_video" boolean,
	"upvote_ratio" integer,
	"send_replies" boolean,
	"raw_data" jsonb
);
--> statement-breakpoint
ALTER TABLE "post" ADD CONSTRAINT "post_project_id_project_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."project"("id") ON DELETE no action ON UPDATE no action;