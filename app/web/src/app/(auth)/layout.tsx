'use client'
import { useUserStore } from '@/store/userStore'
import { redirect, useRouter } from 'next/navigation'

export default function AuthLayout({ children }: { children: React.ReactNode }) {
	const isLoggedIn = useUserStore((state: any) => state.isLoggedIn)
	const loading = useUserStore((state: any) => state.loading)
	const router = useRouter()
	if (loading) {
		return (
			<div className="flex h-screen w-full items-center justify-center">
				<div className="w-10 h-10 bg-primary rounded-full animate-pulse" />
			</div>
		)
	}
	if (!isLoggedIn) {
		return router.replace('/login')
	}
	return <>{children}</>
}
