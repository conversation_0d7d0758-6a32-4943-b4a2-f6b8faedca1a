import { subredditsToDb, addEmbeddings } from './modules/subreddit/subreddit.service'
import { StartWorker } from './modules/posts.service'
import { config } from './config'
import { redditApiRequest } from '@packages/shared'

export const runSubredditToDb = async () => {
	await subredditsToDb()
}

export const runAddEmbeddings = async () => {
	await addEmbeddings()
}

// Example function to test Reddit API authentication
export const testRedditAuth = async () => {
	try {
		// Check if Reddit credentials are configured
		if (!config.reddit.clientId || !config.reddit.clientSecret) {
			console.error(
				'Reddit API credentials not configured. Please set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET environment variables.',
			)
			return
		}

		// Make an authenticated request to Reddit API
		console.log('Testing Reddit API authentication...')
		const response = await redditApiRequest('/api/v1/me', config.reddit.clientId, config.reddit.clientSecret, {
			method: 'GET',
		})

		console.log('Reddit API authentication successful!')
		console.log('API response:', JSON.stringify(response, null, 2))
	} catch (error) {
		console.error('Reddit API authentication failed:', error)
	}
}

const main = async () => {
	const args = process.argv.slice(2)
	const command = args[0]

	switch (command) {
		case 'subreddit':
			runSubredditToDb()
			break
		case 'embeddings':
			runAddEmbeddings()
			break
		case 'test-reddit-auth':
			await testRedditAuth()
			break
		default:
			StartWorker()
	}
}

main()
