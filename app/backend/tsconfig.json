{
	"compilerOptions": {
		"composite": true /* Enable constraints that allow a TypeScript project to be used with project references. */,
		"target": "es2016" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
		"module": "commonjs" /* Specify what module code is generated. */,
		"moduleResolution": "node" /* Specify how TypeScript looks up a file from a given module specifier. */,
		"baseUrl": "." /* Specify the base directory to resolve non-relative module names. */,
		"paths": {
			"@/*": ["src/*"],
			"@app": ["src/index"],
			"@middlewares/*": ["src/middlewares/*"],
			"@modules/*": ["src/modules/*"],
			"@utils/*": ["src/utils/*"],
			"@messages": ["src/config/messages"],
			"@enum": ["src/config/enum"],
			"@constants": ["src/config/const"],
			"@db": ["src/db"],
			"@schema/*": ["src/db/schema/*"],
			"@config/*": ["src/config/*"],
			"@packages/shared": ["../../packages/shared/src"]
		} /* Specify a set of entries that re-map imports to additional lookup locations. */,
		"allowJs": false /* Allow JavaScript files to be a part of your program. Use the 'checkJS' option to get errors from these files. */,
		"sourceMap": true /* Create source map files for emitted JavaScript files. */,
		"esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
		"forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,
		"strict": true /* Enable all strict type-checking options. */,
		"noImplicitAny": false /* Enable error reporting for expressions and declarations with an implied 'any' type. */,
		"skipLibCheck": true /* Skip type checking all .d.ts files. */,
		"resolveJsonModule": true,
		"jsx": "react"
	},
	"references": [
		{
			"path": "../../packages/shared"
		}
	]
}
