import { pgTable, uuid, varchar, timestamp, integer, boolean, uniqueIndex } from 'drizzle-orm/pg-core'

export const plan = pgTable(
	'plan',
	{
		// Primary identifier
		id: uuid('id').defaultRandom().primaryKey(),

		// Plan details
		name: varchar('name', { length: 255 }).notNull(),
		description: varchar('description', { length: 1000 }),

		// Lemonsqueezy data
		lemonsqueezyVariantId: varchar('lemonsqueezy_variant_id', { length: 255 }).notNull().unique(),
		lemonsqueezyProductId: varchar('lemonsqueezy_product_id', { length: 255 }).notNull(),

		// Pricing
		price: integer('price').notNull(), // in cents
		interval: varchar('interval', { length: 20 }).notNull(), // monthly, yearly

		// Features
		maxProjects: integer('max_projects').notNull(),

		// Status
		isActive: boolean('is_active').default(true).notNull(),

		// Timestamps
		createdAt: timestamp('created_at').defaultNow().notNull(),
		updatedAt: timestamp('updated_at').defaultNow().notNull(),
	},
	(table: any) => [uniqueIndex('lemonsqueezy_variant_id_idx').on(table.lemonsqueezyVariantId)],
)
