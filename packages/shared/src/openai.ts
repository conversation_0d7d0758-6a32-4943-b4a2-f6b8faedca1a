import OpenAI from 'openai'

export const ai =
	process.env.OPENAI_API_KEY &&
	new OpenAI({
		apiKey: process.env.OPENAI_API_KEY,
	})

export const generateEmbedding = async (value: string): Promise<number[]> => {
	if (!ai) {
		throw new Error('OPENAI_API_KEY is not set')
	}
	const input = value.replace(/\n/g, ' ')
	const { data } = await ai.embeddings.create({
		model: 'text-embedding-ada-002',
		input,
	})
	return data[0].embedding
}
