{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e": "start-server-and-test dev http://localhost:3000 cypress:run", "type-check": "tsc --noEmit"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.13.4", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@sentry/nextjs": "^9.17.0", "@tanstack/react-query": "^5.75.0", "@types/js-cookie": "^3.0.6", "axios": "^1.9.0", "bullmq": "^5.49.2", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "daisyui": "^5.0.27", "drizzle-kit": "^0.31.0", "drizzle-orm": "^0.43.1", "embla-carousel-react": "^8.6.0", "firebase": "^11.6.0", "firebase-admin": "^13.3.0", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.503.0", "next": "15.3.1", "next-runtime-env": "^3.3.0", "openai": "^4.95.1", "pg": "^8.14.1", "random-words": "^2.0.1", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-select": "^5.10.1", "recharts": "^2.15.3", "stripe": "^18.0.0", "tailwind-merge": "^3.2.0", "vaul": "^1.1.2", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@testing-library/cypress": "^10.0.3", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cypress": "13.6.6", "start-server-and-test": "^2.0.11", "tailwindcss": "^4", "typescript": "^5"}}