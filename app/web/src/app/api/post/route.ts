export const dynamic = 'force-dynamic'
import { verifyAuthToken } from '@/utils/firebase-admin'
import { db, openai, schema } from '@packages/shared'
import { and, eq } from 'drizzle-orm'

export async function POST(request: Request) {
	if (!openai || !openai?.ai) {
		return new Response('OpenAI is not set', { status: 500 })
	}

	// handle auth from cookie
	const auth = await verifyAuthToken()
	if (!auth || !auth.uid) {
		return new Response('Unauthorized', { status: 401 })
	}

	const encoder = new TextEncoder()
	const { projectId, postId, additionalContext, maxWords } = await request.json()
	if (!projectId) {
		return new Response('Project ID is required', { status: 400 })
	}
	const project = await db.query.project.findFirst({
		where: and(eq(schema.project.id, projectId), eq(schema.project.userId, auth.uid)),
	})
	if (!project) {
		return new Response('Project not found', { status: 404 })
	}
	const post = await db.query.post.findFirst({
		where: and(eq(schema.post.id, postId), eq(schema.post.projectId, projectId)),
	})
	if (!post) {
		return new Response('Post not found', { status: 404 })
	}
	const prompt = `
	You are a helpful assistant that generates comments for a post on a reddit post.

	Here is the post:
	Title: ${post.title}
	Content: ${post.selftext}

	Here is the project:
	Title: ${project.name}
	Description: ${project.description}
	Pain Point: ${project.painPoint}
	${additionalContext ? `Additional Context: ${additionalContext}` : ''}

	Instructions:
	- Generate a persuasive comment that subtly promotes the product
	- Keep the comment under ${maxWords || 60} words
	- Make the comment sound natural and conversational
	- Avoid being too promotional or salesy
	`


	const systemPrompt = `System message:
		You are a helpful assistant that generates natural, persuasive comments for a Reddit post. Your goal is to subtly promote a project in a way that blends in with the subreddit's tone and language.

		Instructions:

		Write a friendly, conversational comment that adds value to the discussion.
		Seamlessly introduce the product as a helpful suggestion, not a sales pitch.
		Stay under ${maxWords || 60} words.

		Match the style, tone, and vocabulary typical of the subreddit.

		Follow the 80/20 rule: 80% useful insight or empathy, 20% subtle promotion.

		Don't over-explain or use marketing jargon.
		Critically, your response must *only* promote the project detailed in the 'Pain Point Solved' section. Do not mention, recommend, or allude to any other third-party products, tools, or services, even if they seem relevant.

		Good Example (for r/freelance):
		Totally feel you on juggling multiple clients—time tracking used to eat up my day. I started using TimeStack and it just runs in the background, logging everything. Might be worth checking out.

		Why it works:

		Feels like personal advice
		Product mentioned naturally, not pushed
		Fits the tone of the subreddit

		Bad Example:
		You should check out TimeStack! It's the #1 time-tracking tool for freelancers. Sign up today for a free trial!

		Why it fails:
		Too promotional and salesy
		Sounds like an ad
		Doesn't add value to the conversation
		`

	const userMessage = `
		User input:
		Inputs: 
		Reddit Post 
		Subreddit: ${post.subreddit}
		Title: ${post.title}
		Content: ${post.selftext}

		Pain Point Solved: ${project.painPoint}
		${additionalContext ? `Additional Context: ${additionalContext}` : ''}
`
	const stream = new ReadableStream({
		async start(controller) {
			if (!openai || !openai?.ai) {
				return new Response('OpenAI is not set', { status: 500 })
			}
			try {
				const openaiStream = await openai?.ai?.responses?.create({
					input: `${systemPrompt}\n\n${userMessage}`,
					model: 'gpt-4o',
					stream: true,

				})

				for await (const chunk of openaiStream) {
					if ((chunk as any).delta) {
						console.log((chunk as any).delta.toString())
						// Send just the text part
						controller.enqueue(encoder.encode((chunk as any).delta))
					}
				}
				controller.close()
			} catch (error) {
				console.error('Error in stream:', error)
				controller.error(error)
			}
		},
	})

	return new Response(stream, {
		headers: {
			'Content-Type': 'text/plain; charset=utf-8',
			'Cache-Control': 'no-cache',
		},
	})
}
