'use server'

import { db, schema } from '@packages/shared'
import { createCustomer, createCheckoutSession, createCheckoutSessionWithoutTrial } from '@/services/stripe'
import { sql } from 'drizzle-orm'

/**
 * Create a checkout session for a subscription with trial
 * @param userId The user's ID
 * @param email The user's email
 * @param skipTrial Whether to skip the trial period (for returning customers)
 * @returns Checkout session URL
 */
export async function createTrialCheckout(userId: string, email: string, skipTrial: boolean = false) {
	try {
		// Check if user exists
		const users = await db.select().from(schema.user).where(sql`${schema.user.id} = ${userId}`).limit(1)

		if (users.length === 0) {
			return { success: false, error: 'User not found' }
		}

		// Check if user already has any subscription (active or not)
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(sql`${schema.subscription.userId} = ${userId}`)
			.orderBy(sql`${schema.subscription.createdAt} DESC`)
			.limit(1)

		// If there's an active trial, don't create a new one
		if (
			subscriptions.length > 0 &&
			subscriptions[0].isTrialActive &&
			subscriptions[0].trialEndDate &&
			subscriptions[0].trialEndDate > new Date()
		) {
			return {
				success: false,
				error: 'You already have an active trial',
				subscriptionId: subscriptions[0].id,
			}
		}

		// If the user has had any subscription before (including expired trials)
		if (subscriptions.length > 0) {
			// If they have an active subscription, just return that they already have one
			if (subscriptions.some(sub => sub.status === 'active')) {
				return {
					success: false,
					error: 'You already have an active subscription',
					subscriptionId: subscriptions[0].id,
				}
			}
			
			// If they've had a trial before (even if it's expired), don't allow a new trial
			// But if skipTrial is true, allow them to purchase directly without a trial
			if (!skipTrial) {
				return {
					success: false,
					error: 'You have already used your free trial',
					hadTrial: true,
					subscriptionId: subscriptions[0].id,
				}
			}

			// If skipTrial is true, we'll continue with the checkout process without a trial
		}

		// Check if user already has a Stripe customer ID from a previous subscription
		let stripeCustomerId = '';

		// Look for any existing subscription with a Stripe customer ID
		const existingSubscriptions = await db
			.select()
			.from(schema.subscription)
			.where(sql`${schema.subscription.userId} = ${userId} AND ${schema.subscription.stripeCustomerId} IS NOT NULL`)
			.limit(1);

		if (existingSubscriptions.length > 0 && existingSubscriptions[0].stripeCustomerId) {
			// Reuse the existing Stripe customer ID
			stripeCustomerId = existingSubscriptions[0].stripeCustomerId;
		} else {
			// Create a new Stripe customer only if one doesn't exist
			const customerResult = await createCustomer(email);
			if (!customerResult.success) {
				return { success: false, error: customerResult.error || 'Failed to create customer' };
			}
			stripeCustomerId = customerResult.customer?.id || '';
		}

		// Get the Stripe price ID for the Pro plan
		const priceId = process.env.STRIPE_PRICE_ID
		if (!priceId) {
			return { success: false, error: 'Stripe price ID is not configured' }
		}

		// Create a checkout session with or without trial
		const checkoutResult = skipTrial
			? // Use the no-trial checkout function
			  await createCheckoutSessionWithoutTrial(
					stripeCustomerId,
					priceId,
					{ userId }
			  )
			: // Use the trial checkout function
			  await createCheckoutSession(
					stripeCustomerId,
					priceId,
					3, // 3-day trial
					{ userId }
			  );

		if (!checkoutResult.success) {
			return { success: false, error: checkoutResult.error || 'Failed to create checkout' }
		}

		// Create a pending subscription record
		const subscriptionData: any = {
			userId,
			stripeCustomerId: stripeCustomerId,
			status: 'pending',
			isTrialActive: !skipTrial, // Only set as trial if not skipping trial
			stripePriceId: priceId,
		};

		// Only add trialStartDate if not skipping trial
		if (!skipTrial) {
			subscriptionData.trialStartDate = new Date();
		}

		await db.insert(schema.subscription).values(subscriptionData)

		return { success: true, url: checkoutResult.url }
	} catch (error: any) {
		console.error('Error creating trial checkout:', error)
		return { success: false, error: error.message || 'Failed to create checkout' }
	}
}

/**
 * Check if a user has an active subscription
 * @param userId The user's ID
 * @returns Whether the user has an active subscription and payment status
 */
export async function hasActiveSubscription(userId: string) {
	try {
		// Get the most recent subscription
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(sql`${schema.subscription.userId} = ${userId}`)
			.orderBy(sql`${schema.subscription.createdAt} DESC`)

		

		if (subscriptions.length === 0) {
			return { success: true, hasSubscription: false }
		}

		let subscription = undefined
		const statuses = subscriptions.map(sub => sub.status)
		if(statuses.includes('active')) {
			subscription = subscriptions[statuses.indexOf('active')]
		}
		if(statuses.includes('trialing')) {
			subscription = subscriptions[statuses.indexOf('trialing')]
		}
		if(statuses.includes('past_due')) {
			subscription = subscriptions[statuses.indexOf('past_due')]
		}
		if (!subscription) {
			return { success: true, hasSubscription: false }
		}

		// Check for past_due status - payment needs to be updated
		if (subscription.status === 'past_due') {
			return {
				success: true,
				hasSubscription: false,
				paymentUpdateRequired: true,
				subscriptionId: subscription.id
			}
		}

		// Check subscription status
		if (subscription.status === 'active') {
			return { success: true, hasSubscription: true }
		}

		// Check if trial is active
		if (subscription.status === 'trialing' && subscription.isTrialActive) {
			const now = new Date()
			if (subscription.trialEndDate && subscription.trialEndDate > now) {
				return { success: true, hasSubscription: true }
			}

			// Trial has expired, update subscription
			await db
				.update(schema.subscription)
				.set({
					isTrialActive: false,
					status: 'expired',
					updatedAt: now,
				})
				.where(sql`${schema.subscription.id} = ${subscription.id}`)
		}

		return { success: true, hasSubscription: false }
	} catch (error) {
		console.error('Error checking subscription:', error)
		return { success: false, error: 'Failed to check subscription' }
	}
}

/**
 * Get user subscription details
 * @param userId The user's ID
 * @returns Subscription details
 */
export async function getUserSubscription(userId: string) {
	try {
		// Get the most recent subscription
		const subscriptions = await db
			.select()
			.from(schema.subscription)
			.where(sql`${schema.subscription.userId} = ${userId}`)
			.orderBy(sql`${schema.subscription.createdAt} DESC`)
			.limit(1)
		if (subscriptions.length === 0) {
			return { success: true, hasSubscription: false, subscription: null }
		}

		const subscription = subscriptions[0]

		// Check subscription status
		const hasSubscription =
			subscription.status === 'active' ||
			(subscription.status === 'trial' &&
				subscription.isTrialActive &&
				subscription.trialEndDate &&
				subscription.trialEndDate > new Date())

		return {
			success: true,
			hasSubscription,
			subscription,
			status: subscription.status,
			isTrialActive: subscription.isTrialActive,
		}
	} catch (error) {
		console.error('Error getting subscription:', error)
		return { success: false, error: 'Failed to get subscription' }
	}
}
