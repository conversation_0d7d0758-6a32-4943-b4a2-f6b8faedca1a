"use client"

import { Play } from 'lucide-react'
import { useState } from 'react'
import Image from 'next/image'

const AppDemo = () => {
	const [isPlaying, setIsPlaying] = useState(false)

	return (
		<div className="max-w-5xl mx-auto px-4 mb-24">
			<div className="bg-gray-900 rounded-xl overflow-hidden shadow-2xl aspect-video relative">
				{!isPlaying ? (
					<div 
						className="absolute inset-0 flex items-center justify-center cursor-pointer"
						onClick={() => setIsPlaying(true)}
					>
						<div className="absolute inset-0">
							<Image
								src="/landing/assets/LeadsAutomatic-Demo-Thumbnail.png"
								alt="Video thumbnail"
								fill
								className="object-cover opacity-50"
								priority
							/>
						</div>
						<div className="text-white text-center p-8 relative z-10">
							<div className="w-16 h-16 mx-auto mb-4 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors">
								<Play className="h-8 w-8 text-white fill-white" />
							</div>
							<p className="text-xl font-medium">Watch how LeadsAutomatic works</p>
						</div>
					</div>
				) : (
					<video
						className="w-full h-full object-cover"
						controls
						autoPlay
						onEnded={() => setIsPlaying(false)}
					>
						<source src="/landing/assets/LeadsAutomatic-Demo.mp4" type="video/mp4" />
						Your browser does not support the video tag.
					</video>
				)}
			</div>
		</div>
	)
}

export default AppDemo
