import dayjs from 'dayjs'
import axios from 'axios'
import utc from 'dayjs/plugin/utc'
import { db, schema, mq, openai } from '@packages/shared'
import { type Job, Queue, Worker } from 'bullmq'
import { and, eq } from 'drizzle-orm'
import { config } from '../config'
import { redditApiRequest } from '@packages/shared'

const myQueue = new Queue('parse-posts', mq.options)

// Check if Reddit credentials are available
const hasRedditCredentials = config.reddit.clientId && config.reddit.clientSecret

dayjs.extend(utc)

// Type definition for Reddit response data
interface RedditResponse {
	data: {
		children: Array<{
			kind: string
			data: any
		}>
		after: string | null
	}
}

export const parsePosts = async (projectId: string, subreddit: string) => {
	const unix = dayjs.utc(dayjs().subtract(7, 'day').toISOString()).unix()
	let next = true
	let after = ''
	const posts: string[] = []

	while (next) {
		let responseData: RedditResponse
		const endpoint = `/${subreddit}/new`
		const queryParams = `?limit=50&after=${after}`

		try {
			// Use authenticated API if credentials are available, otherwise fallback to public API
			if (hasRedditCredentials) {
				// Use authenticated Reddit API
				responseData = await redditApiRequest(
					`${endpoint}${queryParams}`,
					config.reddit.clientId,
					config.reddit.clientSecret,
					{ method: 'GET' },
				)
			} else {
				// Fallback to public API
				const { data } = await axios.get(`https://www.reddit.com${endpoint}.json${queryParams}`, {
					headers: { 'User-Agent': 'leadsautomatic/1.0 (by /u/f4yis)' },
				})
				responseData = data
			}

			for (const child of responseData.data.children) {
				const post = child.data
				if (post.created_utc < unix) {
					next = false
					break
				}
				try {
					await db.insert(schema.post).values({
						projectId: projectId,
						redditId: post.id,
						title: post.title,
						author: post.author,
						subreddit: post.subreddit,
						permalink: post.permalink,
						url: post.url,
						selftext: post.selftext,
						created: new Date(post.created_utc * 1000),
						score: post.score,
						numComments: post.num_comments,
						thumbnail: post.thumbnail,
						isOriginalContent: post.is_original_content,
						isVideo: post.is_video,
						upvoteRatio: post.upvote_ratio,
						sendReplies: post.send_replies,

						// Optional: store the full response data
						rawData: post,
					})
				} catch (e) {
					console.log(e)
				}
			}

			// Handle pagination
			const nextAfter = responseData.data.after
			if (!nextAfter) {
				next = false
			} else {
				after = nextAfter
			}
		} catch (error) {
			console.error(`Error fetching posts from ${subreddit}:`, error)
			next = false
		}
	}
}

const checkPost = async (project: typeof schema.project.$inferSelect, post: typeof schema.post.$inferSelect) => {
	const prompt = `Role: You are an AI that evaluates Reddit posts for project relevance.

	Input: A Reddit post (title, content, URL) and a project (name, description, pain points, keywords).

	Evaluation Criteria:
	- Relevant (score 4-5): Post directly addresses project's pain points or use cases
	- Somewhat Relevant (score 2-3): Post mentions related problems or scenarios
	- Not much relevant (score 1): Post is somewhat related to the project but doesn't mention the pain points or use cases
	- Not Relevant (score 0): Post is unrelated or only has superficial keyword matches



	Output JSON:
	{
		"result": boolean,
		"reason": "Brief explanation (max 60 words)",
		"relevanceScore": number (0-5)
	}

	Project:
	Name: ${project.name}
	Description: ${project.description}
	Pain Points: ${project.painPoint}
	Keywords: ${project.keywords}

	Post:
	Title: ${post.title}
	URL: ${post.url}
	Content: ${post.selftext}`
	if (!openai || !openai.ai) {
		throw new Error('OpenAI client is not properly initialized')
	}

	const response = await openai.ai.chat.completions.create({
		model: 'gpt-4o-mini',
		messages: [{ role: 'user', content: prompt }],
		response_format: { type: 'json_object' },
	})

	const result = JSON.parse(response.choices[0].message.content || '{}')
	console.log(result)
	await db
		.update(schema.post)
		.set({
			status: 'completed',
			chosen: result.result || false,
			chosenAt: new Date(),
			chosenReason: result.reason || '',
			relevanceScore: result.relevanceScore || 0,
		})
		.where(eq(schema.post.id, post.id))
}

export const checkPostsWithAi = async (project: typeof schema.project.$inferSelect) => {
	while (true) {
		const posts = await db.query.post.findMany({
			where: and(eq(schema.post.projectId, project.id), eq(schema.post.status, '')),
			limit: 10,
		})
		if (!posts.length) {
			console.log(`No posts to check for ${project.name}`)
			break
		}
		await Promise.all(posts.map((post) => checkPost(project, post)))
		await new Promise((resolve) => setTimeout(resolve, 4000))
	}
}

export const parseSubreddits = async (projectId: string) => {
	const project = await db.query.project.findFirst({
		where: eq(schema.project.id, projectId),
	})
	if (!project) {
		throw new Error('Project not found')
	}

	const subreddits = await db
		.select({
			id: schema.subreddit.prefixedName,
		})
		.from(schema.projectSubreddit)
		.where(and(eq(schema.projectSubreddit.projectId, projectId), eq(schema.projectSubreddit.selected, true)))
		.leftJoin(schema.subreddit, eq(schema.projectSubreddit.subredditId, schema.subreddit.id))
	for (const subreddit of subreddits) {
		console.log(subreddit)
		if (subreddit.id) {
			await parsePosts(projectId, subreddit.id)
			console.log(`Parsed ${subreddit.id}`)
			await checkPostsWithAi(project)
			console.log(`Checked ${subreddit.id}`)
		}
	}
}

export const StartWorker = async () => {
	const worker = new Worker(
		'parse-posts',
		async (job: Job) => {
			if (!job.id) return
			await parseSubreddits(job.id)
			await job.updateProgress('completed')
			await db.update(schema.project).set({ lastScanCompleted: new Date() }).where(eq(schema.project.id, job.id))
			return 'completed'
		},
		mq.options,
	)
}
