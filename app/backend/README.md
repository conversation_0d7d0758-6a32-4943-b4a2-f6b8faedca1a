# Reddit API Integration

This document explains how to set up and use the Reddit API authentication in the backend.

## Overview

The Reddit API integration is implemented as an internal service that doesn't expose any ports or external APIs. Instead, it runs as part of the backend processes and is used by internal services to fetch data from Reddit.

## Setup

1. Create a Reddit API application at https://www.reddit.com/prefs/apps
2. Set your application type to "script"
3. Note your Client ID and Client Secret

## Environment Variables

Add the following environment variables to your system or create a `.env` file in the backend directory:

```
# Reddit API credentials
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
```

## Usage

The application includes internal Reddit API utilities for fetching data. When credentials are provided, it will automatically use OAuth authentication. Otherwise, it will fall back to the public API with rate limitations.

### Testing Authentication

You can test the Reddit API authentication using the command-line utility:

```bash
node --require esbuild-register src/index.ts test-reddit-auth
```

### Using in Internal Code

To make authenticated requests to the Reddit API within the backend code, use the `redditApiRequest` function:

```typescript
import { redditApiRequest } from './modules/reddit/reddit.service';
import { config } from './config';

// Example: Get hot posts from r/programming
const hotPosts = await redditApiRequest(
  '/r/programming/hot',
  config.reddit.clientId,
  config.reddit.clientSecret,
  { method: 'GET' }
);
```

## Benefits of Authentication

- Higher rate limits (60 requests per minute instead of 30)
- Access to private subreddits if the account has access
- More reliable API access with fewer "Too Many Requests" errors

## Security Notes

- The Reddit API credentials are used only within the backend processes
- No APIs or ports are exposed for this functionality
- All communication with Reddit happens from the backend server processes
- User data is not directly exposed through this integration 