import { defineConfig } from 'cypress'

export default defineConfig({
	e2e: {
		baseUrl: 'http://localhost:3000',
		setupNodeEvents(_on, _config) {
			// implement node event listeners here
		},
		// Increase default command timeout
		defaultCommandTimeout: 10000,
		// Increase page load timeout
		pageLoadTimeout: 30000,
		// Increase timeout for cy.visit() command
		responseTimeout: 30000,
		// Increase timeout for cy.request() command
		requestTimeout: 10000,
		// Increase timeout for assertions
		// Cypress doesn't have a specific assertionTimeout setting
		// but defaultCommandTimeout affects assertions
	},
	component: {
		devServer: {
			framework: 'next',
			bundler: 'webpack',
		},
	},
	// Retry failed tests
	retries: {
		// Configure retry attempts for `cypress run`
		runMode: 2,
		// Configure retry attempts for `cypress open`
		openMode: 1,
	},
})
