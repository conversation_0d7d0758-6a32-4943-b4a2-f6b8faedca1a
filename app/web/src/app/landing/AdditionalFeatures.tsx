import { Globe, Edit, Infinity, LayoutGrid, Users, MessageCircleQuestion } from 'lucide-react'

interface FeatureCardProps {
	icon: React.ReactNode
	title: string
	description: React.ReactNode
}

const FeatureCard = ({ icon, title, description }: FeatureCardProps) => {
	return (
		<div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 h-full">
			<div className="flex flex-col h-full">
				<div className="bg-[#fff1eb] p-3 rounded-full w-12 h-12 flex items-center justify-center text-[#FF4500] mb-4">
					{icon}
				</div>
				<h3 className="text-xl font-bold mb-2">{title}</h3>
				<p className="text-gray-600 text-sm">{description}</p>
			</div>
		</div>
	)
}

const AdditionalFeatures = () => {
	const features = [
		{
			icon: <Globe className="w-6 h-6" />,
			title: 'Reply in your tone',
			description: (
				<>
					Generate high-quality responses that match your communication style. Our AI adapts to your preferred
					tone and voice for authentic Reddit engagement.
				</>
			),
		},
		{
			icon: <Edit className="w-6 h-6" />,
			title: '<PERSON> replies with AI',
			description: (
				<>
					Easily edit suggested replies to meet your exact expectations, ensuring every word aligns with your
					vision for authentic Reddit communication.
				</>
			),
		},
		{
			icon: <Infinity className="w-6 h-6" />,
			title: 'Generate unlimited opportunities',
			description: (
				<>
					Discover unlimited sets of Reddit engagement opportunities until you find the perfect match for your
					startup's visibility goals.
				</>
			),
		},
		{
			icon: <LayoutGrid className="w-6 h-6" />,
			title: 'Multi-subreddit monitoring',
			description: (
				<>
					Scale your Reddit presence by monitoring multiple relevant subreddits. Add new communities to your
					package whenever needed.
				</>
			),
		},
		{
			icon: <Users className="w-6 h-6" />,
			title: 'We want feedback',
			description: (
				<>
					We are still new and we want to hear your feedback. We are working on a lot of features and we want
					to make sure we are building the right things.
				</>
			),
		},
		{
			icon: <MessageCircleQuestion className="w-6 h-6" />,
			title: 'Expert support',
			description: (
				<>
					Get expert assistance whenever you need it, ensuring smooth operation of all platform features for
					maximum Reddit success.
				</>
			),
		},
	]

	return (
		<section className="py-20 bg-gray-50">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-16">
					<div className="relative">
						<h2 className="text-3xl sm:text-5xl font-bold mb-4">
							And so
							<span className="text-[#FF4500]"> much more </span>
							you need
							<div className="flex justify-center">to do your best work.</div>
						</h2>
					</div>
				</div>

				<div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
					{features.map((feature, index) => (
						<FeatureCard
							key={index}
							icon={feature.icon}
							title={feature.title}
							description={feature.description}
						/>
					))}
				</div>
			</div>
		</section>
	)
}

export default AdditionalFeatures
