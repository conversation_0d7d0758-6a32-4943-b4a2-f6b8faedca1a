import { Twitter } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

const FooterSection = ({ title, links }: { title: string; links: { label: string; href: string }[] }) => (
	<div className="mb-8 md:mb-0">
		<h3 className="text-sm font-semibold mb-4">{title}</h3>
		<ul className="space-y-3">
			{links.map((link, index) => (
				<li key={index}>
					<Link href={link.href} className="text-sm text-gray-500 hover:text-[#FF4500]">
						{link.label}
					</Link>
				</li>
			))}
		</ul>
	</div>
)

const Footer = () => {
	const productLinks = [
		{ label: 'How it works', href: '/#how-it-works' },
		{ label: 'Use cases', href: '/#why-LeadsAutomatic' },
		{ label: 'Pricing', href: '/#pricing' },
		{ label: 'FAQ', href: '/#faq' },
	]

	const legalLinks = [
		{ label: 'Privacy policy', href: '/privacy' },
		{ label: 'Terms of service', href: '/terms' },
	]

	return (
		<footer className="bg-white pt-16 pb-8 border-t border-gray-100">
			<div className="max-w-6xl mx-auto px-4">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-16">
					<div className="lg:col-span-2">
						<div className="flex items-center space-x-2">
							<Image
								src="/landing/assets/lda_logo_red_mark.svg"
								alt="LeadsAutomatic Logo"
								width={200}
								height={46}
							/>
						</div>
						<p className="text-gray-500 text-sm mt-2 mb-6 max-w-md">
							LeadsAutomatic helps small software startups get discovered on Reddit by finding high-impact
							posts and generating smart reply suggestions.
						</p>
						<div className="flex space-x-4">
							<Link href="https://x.com/marcschiwek" className="text-gray-400 hover:text-[#FF4500]" aria-label="Twitter">
								<Twitter className="h-5 w-5" />
							</Link>
						</div>
					</div>

					<FooterSection title="Product" links={productLinks} />
					<div className="flex lg:col-span-2 space-x-4">
						<Link href="https://startupfa.me/s/leadsautomatic.com?utm_source=leadsautomatic.com" target="_blank" rel="noopener">
							<img src="https://startupfa.me/badges/featured/default.webp" alt="Featured on Startup Fame" className="h-10 w-auto" />
						</Link>
						<Link href="https://tinylaun.ch" target="_blank" rel="noopener">
							<img
								src="https://tinylaun.ch/tinylaunch_badge_launching_soon.svg"
								alt="TinyLaunch Badge"
								className="h-10 w-auto"
							/>
						</Link>
					</div>
				</div>

				<div className="border-t border-gray-100 pt-8 flex flex-col md:flex-row justify-between">
					<p className="text-gray-400 text-sm mb-4 md:mb-0">
						© {new Date().getFullYear()} LeadsAutomatic by PEWEO SARL. All rights reserved.
					</p>
					<div className="flex flex-wrap gap-x-6 gap-y-2">
						{legalLinks.map((link, index) => (
							<Link key={index} href={link.href} className="text-gray-400 hover:text-gray-600 text-sm">
								{link.label}
							</Link>
						))}
					</div>
				</div>
			</div>
		</footer>
	)
}

export default Footer
