ALTER TABLE "user" DROP CONSTRAINT "user_firebase_uid_unique";--> statement-breakpoint
DROP INDEX "firebase_uid_idx";--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "id" SET DATA TYPE varchar(128);--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "id" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "firebase_uid";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "full_name";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "first_name";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "last_name";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "profile_picture";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "is_active";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "email_verified";